<template>
  <div class="comment-component">
    <div class="header">
      <h2>Comment 管理</h2>
      <button @click="showCreateForm = true" class="btn btn-primary">
        添加 Comment
      </button>
    </div>

    <!-- 列表 -->
    <div class="list-container">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else>
        <div v-for="item in items" :key="item.id" class="item-card">
          <div class="item-content">
            <div class="field">
              <label>Content:</label>
              <span>{{ item.content }}</span>
            </div>
            <div class="field">
              <label>Status:</label>
              <span>{{ item.status }}</span>
            </div>
            <div class="field">
              <label>Is Anonymous:</label>
              <span>{{ item.is_anonymous }}</span>
            </div>
          </div>
          <div class="item-actions">
            <button @click="editItem(item)" class="btn btn-sm btn-secondary">编辑</button>
            <button @click="deleteItem(item.id)" class="btn btn-sm btn-danger">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑表单 -->
    <div v-if="showCreateForm || editingItem" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingItem ? '编辑' : '创建' }} Comment</h3>
          <button @click="closeForm" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="submitForm" class="form">
          <div class="form-group">
            <label for="content">Content:</label>
            <textarea
              id="content"
              v-model="form.content"
              required
            />
          </div>
          <div class="form-group">
            <label for="status">Status:</label>
            <input type="text"
              id="status"
              v-model="form.status"
              required
            />
          </div>
          <div class="form-group">
            <label for="is_anonymous">Is Anonymous:</label>
            <input type="checkbox"
              id="is_anonymous"
              v-model="form.is_anonymous"
              required
            />
          </div>
          <div class="form-group">
            <label for="author_name">Author Name:</label>
            <input type="text"
              id="author_name"
              v-model="form.author_name"
              required
            />
          </div>
          <div class="form-group">
            <label for="author_email">Author Email:</label>
            <input type="text"
              id="author_email"
              v-model="form.author_email"
              required
            />
          </div>
          <div class="form-group">
            <label for="author_website">Author Website:</label>
            <input type="text"
              id="author_website"
              v-model="form.author_website"
              required
            />
          </div>
          <div class="form-group">
            <label for="ip_address">Ip Address:</label>
            <input type="text"
              id="ip_address"
              v-model="form.ip_address"
              required
            />
          </div>
          <div class="form-group">
            <label for="user_agent">User Agent:</label>
            <input type="text"
              id="user_agent"
              v-model="form.user_agent"
              required
            />
          </div>
          <div class="form-group">
            <label for="like_count">Like Count:</label>
            <input type="number"
              id="like_count"
              v-model="form.like_count"
              required
            />
          </div>
          <div class="form-group">
            <label for="dislike_count">Dislike Count:</label>
            <input type="number"
              id="dislike_count"
              v-model="form.dislike_count"
              required
            />
          </div>
          <div class="form-group">
            <label for="author">Author:</label>
            <select
              id="author"
              v-model="form.author"
              required
            />
          </div>
          <div class="form-group">
            <label for="article">Article:</label>
            <select
              id="article"
              v-model="form.article"
              required
            />
          </div>
          <div class="form-group">
            <label for="parent">Parent:</label>
            <select
              id="parent"
              v-model="form.parent"
              required
            />
          </div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              {{ editingItem ? '更新' : '创建' }}
            </button>
            <button type="button" @click="closeForm" class="btn btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useCommentAPI } from '../composables/useCommentAPI'

export default {
  name: 'CommentComponent',
  setup() {
    const { items, loading, createComment, updateComment, deleteComment, fetchComments } = useCommentAPI()

    const showCreateForm = ref(false)
    const editingItem = ref(null)
    const form = ref({
      content: '',
      status: '',
      is_anonymous: false,
      author_name: '',
      author_email: '',
      author_website: '',
      ip_address: '',
      user_agent: '',
      like_count: 0,
      dislike_count: 0,
      author: '',
      article: '',
      parent: '',
    })

    const editItem = (item) => {
      editingItem.value = item
      form.value = { ...item }
    }

    const closeForm = () => {
      showCreateForm.value = false
      editingItem.value = null
      resetForm()
    }

    const resetForm = () => {
      form.value = {
        content: '',
        status: '',
        is_anonymous: false,
        author_name: '',
        author_email: '',
        author_website: '',
        ip_address: '',
        user_agent: '',
        like_count: 0,
        dislike_count: 0,
        author: '',
        article: '',
        parent: '',
      }
    }

    const submitForm = async () => {
      try {
        if (editingItem.value) {
          await updateComment(editingItem.value.id, form.value)
        } else {
          await createComment(form.value)
        }
        closeForm()
        await fetchComments()
      } catch (error) {
        console.error('提交失败:', error)
      }
    }

    const deleteItem = async (id) => {
      if (confirm('确定要删除这个Comment吗？')) {
        try {
          await deleteComment(id)
          await fetchComments()
        } catch (error) {
          console.error('删除失败:', error)
        }
      }
    }

    onMounted(() => {
      fetchComments()
    })

    return {
      items,
      loading,
      showCreateForm,
      editingItem,
      form,
      editItem,
      closeForm,
      submitForm,
      deleteItem
    }
  }
}
</script>

<style scoped>
.comment-component {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.item-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field {
  margin-bottom: 8px;
}

.field label {
  font-weight: bold;
  margin-right: 8px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}
</style>
