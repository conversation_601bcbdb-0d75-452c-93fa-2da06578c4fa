# 📰 企业级项目教程 04 - 内容管理系统

本教程将指导您构建一个功能完整的企业级内容管理系统（CMS），包括文章管理、分类标签、评论系统、内容审核等核心功能。

## 🎯 本教程目标

完成本教程后，您将：
- ✅ 理解企业级 CMS 系统的设计原理
- ✅ 实现完整的文章发布和管理流程
- ✅ 构建灵活的分类和标签系统
- ✅ 实现评论和互动功能
- ✅ 掌握内容审核和版本控制
- ✅ 了解 SEO 优化和内容分发

## 📋 前置要求

- 完成 [文件存储系统](企业级项目教程-03-文件存储.md)
- 了解内容管理系统基本概念
- 理解 SEO 基础知识

## 🏗️ 内容管理系统架构

### 系统组件
```
内容管理系统
├── 📝 内容模块 (Content)
│   ├── 文章管理
│   ├── 页面管理
│   ├── 草稿系统
│   └── 版本控制
├── 🏷️ 分类模块 (Categories)
│   ├── 层级分类
│   ├── 标签系统
│   ├── 分类权限
│   └── 分类统计
├── 💬 互动模块 (Interaction)
│   ├── 评论系统
│   ├── 点赞收藏
│   ├── 分享功能
│   └── 用户反馈
├── 🔍 搜索模块 (Search)
│   ├── 全文搜索
│   ├── 标签搜索
│   ├── 高级筛选
│   └── 搜索统计
├── 📊 统计模块 (Analytics)
│   ├── 访问统计
│   ├── 内容分析
│   ├── 用户行为
│   └── 报表生成
└── 🛡️ 审核模块 (Moderation)
    ├── 内容审核
    ├── 评论审核
    ├── 举报处理
    └── 自动过滤
```

## 📝 第一步：内容模型设计

### 1.1 使用代码生成工具快速创建

我们可以使用项目内置的代码生成工具快速创建 CMS 模块：

```bash
# 1. 首先预览生成内容
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend --dry-run

# 2. 实际生成代码
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend

# 3. 将应用添加到 INSTALLED_APPS
# 在 core/settings/base.py 的 LOCAL_APPS 中取消注释 "apps.cms"

# 4. 生成迁移文件
python manage.py makemigrations cms

# 5. 执行迁移
python manage.py migrate

# 6. 生成权限
python manage.py auto_generate_permissions cms --create-roles

# 7. 添加 API 路由（生成代码后需要手动添加）
# 在 core/urls.py 中添加 CMS 路由

# 这将生成以下模型：
# - Article (文章)
# - Category (分类)
# - Tag (标签)
# - Comment (评论)
# - ArticleView (文章浏览记录)
# - ArticleLike (文章点赞)
# - Newsletter (邮件订阅)
```

### 1.2 配置 API 路由

生成代码后，需要在 `core/urls.py` 中添加 CMS API 路由：

```python
# core/urls.py
# 在导入部分添加
from apps.cms.api import cms_router

# 在 API 路由部分添加
api.add_router("/cms/", cms_router, tags=["Content Management"])
```

### 1.3 查看生成的模型

生成的模型位于 `apps/cms/models.py`，让我们查看关键模型：

```python
# apps/cms/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()

class Category(BaseModel):
    """分类模型"""
    name = models.CharField(max_length=100, verbose_name=_('名称'))
    slug = models.SlugField(max_length=100, unique=True, verbose_name=_('URL别名'))
    description = models.TextField(blank=True, verbose_name=_('描述'))
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='children',
        verbose_name=_('父分类')
    )
    is_active = models.BooleanField(default=True, verbose_name=_('是否启用'))
    sort_order = models.IntegerField(default=0, verbose_name=_('排序'))
    
    # SEO 字段
    seo_title = models.CharField(max_length=200, blank=True, verbose_name=_('SEO标题'))
    seo_description = models.TextField(blank=True, verbose_name=_('SEO描述'))
    
    # 统计字段
    article_count = models.PositiveIntegerField(default=0, verbose_name=_('文章数量'))
    
    class Meta:
        verbose_name = _('分类')
        verbose_name_plural = _('分类')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['parent']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return self.name
    
    @property
    def full_path(self):
        """获取完整路径"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name
    
    def get_descendants(self):
        """获取所有子分类"""
        descendants = []
        for child in self.children.all():
            descendants.append(child)
            descendants.extend(child.get_descendants())
        return descendants

class Tag(BaseModel):
    """标签模型"""
    name = models.CharField(max_length=50, unique=True, verbose_name=_('名称'))
    slug = models.SlugField(max_length=50, unique=True, verbose_name=_('URL别名'))
    color = models.CharField(max_length=7, default='#007bff', verbose_name=_('颜色'))
    is_active = models.BooleanField(default=True, verbose_name=_('是否启用'))
    
    # 统计字段
    article_count = models.PositiveIntegerField(default=0, verbose_name=_('文章数量'))
    
    class Meta:
        verbose_name = _('标签')
        verbose_name_plural = _('标签')
        ordering = ['name']
    
    def __str__(self):
        return self.name

class Article(BaseModel):
    """文章模型"""
    
    # 文章状态
    STATUS_DRAFT = 'draft'
    STATUS_PUBLISHED = 'published'
    STATUS_ARCHIVED = 'archived'
    STATUS_DELETED = 'deleted'
    
    STATUS_CHOICES = [
        (STATUS_DRAFT, _('草稿')),
        (STATUS_PUBLISHED, _('已发布')),
        (STATUS_ARCHIVED, _('已归档')),
        (STATUS_DELETED, _('已删除')),
    ]
    
    # 基本信息
    title = models.CharField(max_length=200, verbose_name=_('标题'))
    slug = models.SlugField(max_length=200, unique=True, verbose_name=_('URL别名'))
    content = models.TextField(verbose_name=_('内容'))
    summary = models.TextField(max_length=500, blank=True, verbose_name=_('摘要'))
    
    # 分类和标签
    category = models.ForeignKey(
        Category, 
        on_delete=models.SET_NULL, 
        null=True,
        related_name='articles',
        verbose_name=_('分类')
    )
    tags = models.ManyToManyField(Tag, blank=True, verbose_name=_('标签'))
    
    # 作者和权限
    author = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='authored_articles',
        verbose_name=_('作者')
    )
    
    # 发布设置
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default=STATUS_DRAFT,
        verbose_name=_('状态')
    )
    is_featured = models.BooleanField(default=False, verbose_name=_('是否推荐'))
    is_top = models.BooleanField(default=False, verbose_name=_('是否置顶'))
    
    # 发布时间
    published_at = models.DateTimeField(null=True, blank=True, verbose_name=_('发布时间'))
    
    # 统计信息
    view_count = models.PositiveIntegerField(default=0, verbose_name=_('浏览次数'))
    like_count = models.PositiveIntegerField(default=0, verbose_name=_('点赞次数'))
    comment_count = models.PositiveIntegerField(default=0, verbose_name=_('评论次数'))
    
    # SEO 优化
    seo_title = models.CharField(max_length=200, blank=True, verbose_name=_('SEO标题'))
    seo_description = models.TextField(max_length=300, blank=True, verbose_name=_('SEO描述'))
    seo_keywords = models.CharField(max_length=200, blank=True, verbose_name=_('SEO关键词'))
    
    # 媒体文件
    featured_image = models.ForeignKey(
        'storage.StorageFile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='featured_articles',
        verbose_name=_('特色图片')
    )
    
    class Meta:
        verbose_name = _('文章')
        verbose_name_plural = _('文章')
        ordering = ['-is_top', '-published_at', '-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['category']),
            models.Index(fields=['author']),
            models.Index(fields=['published_at']),
            models.Index(fields=['is_featured']),
        ]
    
    def __str__(self):
        return self.title
    
    @property
    def is_published(self):
        """检查是否已发布"""
        return self.status == self.STATUS_PUBLISHED
    
    @property
    def reading_time(self):
        """估算阅读时间（分钟）"""
        word_count = len(self.content.split())
        return max(1, word_count // 200)  # 假设每分钟阅读200字
    
    def get_absolute_url(self):
        """获取文章URL"""
        return f"/articles/{self.slug}/"
    
    def get_related_articles(self, limit=5):
        """获取相关文章"""
        related = Article.objects.filter(
            category=self.category,
            status=self.STATUS_PUBLISHED
        ).exclude(id=self.id)
        
        # 如果同分类文章不够，从相同标签中获取
        if related.count() < limit:
            tag_ids = self.tags.values_list('id', flat=True)
            tag_related = Article.objects.filter(
                tags__in=tag_ids,
                status=self.STATUS_PUBLISHED
            ).exclude(id=self.id).distinct()
            
            related = related.union(tag_related)
        
        return related[:limit]

class Comment(BaseModel):
    """评论模型"""
    
    # 评论状态
    STATUS_PENDING = 'pending'
    STATUS_APPROVED = 'approved'
    STATUS_REJECTED = 'rejected'
    STATUS_SPAM = 'spam'
    
    STATUS_CHOICES = [
        (STATUS_PENDING, _('待审核')),
        (STATUS_APPROVED, _('已通过')),
        (STATUS_REJECTED, _('已拒绝')),
        (STATUS_SPAM, _('垃圾评论')),
    ]
    
    # 基本信息
    content = models.TextField(verbose_name=_('内容'))
    author = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name=_('作者')
    )
    article = models.ForeignKey(
        Article, 
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name=_('文章')
    )
    
    # 回复功能
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='replies',
        verbose_name=_('父评论')
    )
    
    # 审核状态
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default=STATUS_PENDING,
        verbose_name=_('状态')
    )
    
    # 统计信息
    like_count = models.PositiveIntegerField(default=0, verbose_name=_('点赞次数'))
    
    # 技术信息
    ip_address = models.GenericIPAddressField(verbose_name=_('IP地址'))
    user_agent = models.TextField(blank=True, verbose_name=_('用户代理'))
    
    class Meta:
        verbose_name = _('评论')
        verbose_name_plural = _('评论')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['article']),
            models.Index(fields=['author']),
            models.Index(fields=['status']),
            models.Index(fields=['parent']),
        ]
    
    def __str__(self):
        return f"{self.author.username} 对 {self.article.title} 的评论"
    
    @property
    def is_approved(self):
        """检查是否已通过审核"""
        return self.status == self.STATUS_APPROVED
    
    def get_replies(self):
        """获取回复"""
        return self.replies.filter(status=self.STATUS_APPROVED)
```

## 🔧 第二步：内容管理服务

### 2.1 文章管理服务

```python
# apps/cms/services.py
from typing import List, Optional, Dict, Any
from django.db.models import Q, F
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import Article, Category, Tag, Comment

User = get_user_model()

class ArticleService:
    """文章管理服务"""
    
    @staticmethod
    def create_article(user: User, data: Dict[str, Any]) -> Article:
        """创建文章"""
        # 处理标签
        tag_names = data.pop('tags', [])
        
        # 创建文章
        article = Article.objects.create(
            author=user,
            owner=user,
            **data
        )
        
        # 处理标签
        if tag_names:
            tags = []
            for tag_name in tag_names:
                tag, created = Tag.objects.get_or_create(
                    name=tag_name,
                    defaults={'slug': tag_name.lower().replace(' ', '-')}
                )
                tags.append(tag)
            article.tags.set(tags)
        
        return article
    
    @staticmethod
    def publish_article(article: Article) -> Article:
        """发布文章"""
        if article.status != Article.STATUS_PUBLISHED:
            article.status = Article.STATUS_PUBLISHED
            article.published_at = timezone.now()
            article.save()
            
            # 更新分类文章计数
            if article.category:
                article.category.article_count = F('article_count') + 1
                article.category.save()
            
            # 更新标签文章计数
            for tag in article.tags.all():
                tag.article_count = F('article_count') + 1
                tag.save()
        
        return article
    
    @staticmethod
    def get_published_articles(
        category: Optional[Category] = None,
        tags: Optional[List[Tag]] = None,
        search: Optional[str] = None,
        featured_only: bool = False
    ) -> List[Article]:
        """获取已发布的文章"""
        queryset = Article.objects.filter(status=Article.STATUS_PUBLISHED)
        
        if category:
            queryset = queryset.filter(category=category)
        
        if tags:
            queryset = queryset.filter(tags__in=tags).distinct()
        
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(summary__icontains=search)
            )
        
        if featured_only:
            queryset = queryset.filter(is_featured=True)
        
        return queryset.select_related('category', 'author').prefetch_related('tags')
    
    @staticmethod
    def increment_view_count(article: Article):
        """增加浏览次数"""
        Article.objects.filter(id=article.id).update(
            view_count=F('view_count') + 1
        )
    
    @staticmethod
    def get_article_statistics(article: Article) -> Dict[str, Any]:
        """获取文章统计信息"""
        return {
            'view_count': article.view_count,
            'like_count': article.like_count,
            'comment_count': article.comment_count,
            'reading_time': article.reading_time,
            'published_days': (timezone.now() - article.published_at).days if article.published_at else 0
        }

class CommentService:
    """评论管理服务"""
    
    @staticmethod
    def create_comment(
        user: User, 
        article: Article, 
        content: str,
        parent: Optional[Comment] = None,
        ip_address: str = '',
        user_agent: str = ''
    ) -> Comment:
        """创建评论"""
        comment = Comment.objects.create(
            content=content,
            author=user,
            article=article,
            parent=parent,
            ip_address=ip_address,
            user_agent=user_agent,
            owner=user
        )
        
        # 更新文章评论计数
        Article.objects.filter(id=article.id).update(
            comment_count=F('comment_count') + 1
        )
        
        return comment
    
    @staticmethod
    def approve_comment(comment: Comment) -> Comment:
        """审核通过评论"""
        comment.status = Comment.STATUS_APPROVED
        comment.save()
        return comment
    
    @staticmethod
    def get_article_comments(article: Article, approved_only: bool = True) -> List[Comment]:
        """获取文章评论"""
        queryset = Comment.objects.filter(article=article, parent=None)
        
        if approved_only:
            queryset = queryset.filter(status=Comment.STATUS_APPROVED)
        
        return queryset.select_related('author').prefetch_related('replies')

class CategoryService:
    """分类管理服务"""
    
    @staticmethod
    def get_category_tree() -> List[Category]:
        """获取分类树"""
        return Category.objects.filter(parent=None, is_active=True).prefetch_related('children')
    
    @staticmethod
    def get_popular_categories(limit: int = 10) -> List[Category]:
        """获取热门分类"""
        return Category.objects.filter(
            is_active=True,
            article_count__gt=0
        ).order_by('-article_count')[:limit]

class SearchService:
    """搜索服务"""
    
    @staticmethod
    def search_articles(
        query: str,
        category: Optional[Category] = None,
        tags: Optional[List[str]] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> List[Article]:
        """搜索文章"""
        queryset = Article.objects.filter(status=Article.STATUS_PUBLISHED)
        
        # 全文搜索
        if query:
            queryset = queryset.filter(
                Q(title__icontains=query) |
                Q(content__icontains=query) |
                Q(summary__icontains=query) |
                Q(seo_keywords__icontains=query)
            )
        
        # 分类过滤
        if category:
            queryset = queryset.filter(category=category)
        
        # 标签过滤
        if tags:
            queryset = queryset.filter(tags__name__in=tags).distinct()
        
        # 日期过滤
        if date_from:
            queryset = queryset.filter(published_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(published_at__lte=date_to)
        
        return queryset.select_related('category', 'author').prefetch_related('tags')
```

## 📊 第三步：内容统计和分析

### 3.1 统计服务

```python
# apps/cms/services.py (继续添加)
from django.db.models import Count, Sum, Avg
from datetime import datetime, timedelta

class AnalyticsService:
    """内容分析服务"""
    
    @staticmethod
    def get_content_overview() -> Dict[str, Any]:
        """获取内容概览"""
        return {
            'total_articles': Article.objects.count(),
            'published_articles': Article.objects.filter(status=Article.STATUS_PUBLISHED).count(),
            'draft_articles': Article.objects.filter(status=Article.STATUS_DRAFT).count(),
            'total_categories': Category.objects.filter(is_active=True).count(),
            'total_tags': Tag.objects.filter(is_active=True).count(),
            'total_comments': Comment.objects.filter(status=Comment.STATUS_APPROVED).count(),
            'total_views': Article.objects.aggregate(total=Sum('view_count'))['total'] or 0,
        }
    
    @staticmethod
    def get_popular_articles(days: int = 30, limit: int = 10) -> List[Article]:
        """获取热门文章"""
        date_from = timezone.now() - timedelta(days=days)
        return Article.objects.filter(
            status=Article.STATUS_PUBLISHED,
            published_at__gte=date_from
        ).order_by('-view_count')[:limit]
    
    @staticmethod
    def get_author_statistics(user: User) -> Dict[str, Any]:
        """获取作者统计"""
        articles = Article.objects.filter(author=user)
        
        return {
            'total_articles': articles.count(),
            'published_articles': articles.filter(status=Article.STATUS_PUBLISHED).count(),
            'total_views': articles.aggregate(total=Sum('view_count'))['total'] or 0,
            'total_likes': articles.aggregate(total=Sum('like_count'))['total'] or 0,
            'total_comments': articles.aggregate(total=Sum('comment_count'))['total'] or 0,
            'avg_views_per_article': articles.aggregate(avg=Avg('view_count'))['avg'] or 0,
        }
    
    @staticmethod
    def get_trending_tags(days: int = 30, limit: int = 20) -> List[Dict[str, Any]]:
        """获取热门标签"""
        date_from = timezone.now() - timedelta(days=days)
        
        return Tag.objects.filter(
            articles__status=Article.STATUS_PUBLISHED,
            articles__published_at__gte=date_from
        ).annotate(
            recent_article_count=Count('articles')
        ).order_by('-recent_article_count')[:limit]
```

## 🔧 第四步：验证系统功能

### 4.1 检查生成的文件

确认以下文件已正确生成：

```bash
# 检查应用结构
ls -la apps/cms/
# 应该包含：__init__.py, models.py, admin.py, api.py, services.py, schemas.py

# 检查迁移文件
ls -la apps/cms/migrations/
# 应该包含：__init__.py, 0001_initial.py

# 检查前端组件（如果使用了 --with-frontend）
ls -la frontend/src/components/cms/
# 应该包含各个模型的 Vue 组件
```

### 4.2 测试 API 接口

启动开发服务器并测试 API：

```bash
# 启动开发服务器
python manage.py runserver

# 访问 API 文档
# http://localhost:8000/docs/
# 查看 Content Management 分组下的 API 接口
```

### 4.3 测试数据库操作

```python
# 在 Django shell 中测试
python manage.py shell

# 测试模型创建
from apps.cms.models import Category, Tag, Article
from apps.users.models import User

# 创建测试数据
user = User.objects.first()
category = Category.objects.create(name="技术", slug="tech")
tag = Tag.objects.create(name="Python", slug="python")

article = Article.objects.create(
    title="测试文章",
    slug="test-article",
    content="这是一篇测试文章",
    summary="测试摘要",
    author=user,
    category=category,
    status="published"
)
article.tags.add(tag)
```

## ✅ 完成检查

完成本教程后，您应该能够：

- [ ] 理解企业级 CMS 系统的架构设计
- [ ] 使用代码生成工具快速创建 CMS 模块
- [ ] 正确配置应用和路由
- [ ] 执行数据库迁移和权限生成
- [ ] 实现文章的创建、编辑、发布流程
- [ ] 管理分类和标签系统
- [ ] 实现评论和互动功能
- [ ] 进行内容统计和分析
- [ ] 验证系统功能正常运行

## 📖 下一步

恭喜！您已经构建了一个功能完整的企业级内容管理系统。

**接下来学习**：
- [API 接口开发](企业级项目教程-05-API开发.md) - 构建完整的 RESTful API

**相关文档**：
- [代码生成工具指南](代码生成工具指南.md) - 了解更多代码生成功能
- [API使用指南](API使用指南.md) - CMS API 详细说明

---

**🎉 内容管理系统完成！** 您现在拥有了企业级的内容管理功能，支持文章发布、分类管理、评论互动等完整功能。
