<template>
  <div class="newsletter-component">
    <div class="header">
      <h2>Newsletter 管理</h2>
      <button @click="showCreateForm = true" class="btn btn-primary">
        添加 Newsletter
      </button>
    </div>

    <!-- 列表 -->
    <div class="list-container">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else>
        <div v-for="item in items" :key="item.id" class="item-card">
          <div class="item-content">
            <div class="field">
              <label>Email:</label>
              <span>{{ item.email }}</span>
            </div>
            <div class="field">
              <label>Name:</label>
              <span>{{ item.name }}</span>
            </div>
            <div class="field">
              <label>Is Active:</label>
              <span>{{ item.is_active }}</span>
            </div>
          </div>
          <div class="item-actions">
            <button @click="editItem(item)" class="btn btn-sm btn-secondary">编辑</button>
            <button @click="deleteItem(item.id)" class="btn btn-sm btn-danger">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑表单 -->
    <div v-if="showCreateForm || editingItem" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingItem ? '编辑' : '创建' }} Newsletter</h3>
          <button @click="closeForm" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="submitForm" class="form">
          <div class="form-group">
            <label for="email">Email:</label>
            <input type="text"
              id="email"
              v-model="form.email"
              required
            />
          </div>
          <div class="form-group">
            <label for="name">Name:</label>
            <input type="text"
              id="name"
              v-model="form.name"
              required
            />
          </div>
          <div class="form-group">
            <label for="is_active">Is Active:</label>
            <input type="checkbox"
              id="is_active"
              v-model="form.is_active"
              required
            />
          </div>
          <div class="form-group">
            <label for="subscribed_at">Subscribed At:</label>
            <input type="datetime-local"
              id="subscribed_at"
              v-model="form.subscribed_at"
              required
            />
          </div>
          <div class="form-group">
            <label for="unsubscribed_at">Unsubscribed At:</label>
            <input type="datetime-local"
              id="unsubscribed_at"
              v-model="form.unsubscribed_at"
              required
            />
          </div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              {{ editingItem ? '更新' : '创建' }}
            </button>
            <button type="button" @click="closeForm" class="btn btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useNewsletterAPI } from '../composables/useNewsletterAPI'

export default {
  name: 'NewsletterComponent',
  setup() {
    const { items, loading, createNewsletter, updateNewsletter, deleteNewsletter, fetchNewsletters } = useNewsletterAPI()

    const showCreateForm = ref(false)
    const editingItem = ref(null)
    const form = ref({
      email: '',
      name: '',
      is_active: false,
      subscribed_at: '',
      unsubscribed_at: '',
    })

    const editItem = (item) => {
      editingItem.value = item
      form.value = { ...item }
    }

    const closeForm = () => {
      showCreateForm.value = false
      editingItem.value = null
      resetForm()
    }

    const resetForm = () => {
      form.value = {
        email: '',
        name: '',
        is_active: false,
        subscribed_at: '',
        unsubscribed_at: '',
      }
    }

    const submitForm = async () => {
      try {
        if (editingItem.value) {
          await updateNewsletter(editingItem.value.id, form.value)
        } else {
          await createNewsletter(form.value)
        }
        closeForm()
        await fetchNewsletters()
      } catch (error) {
        console.error('提交失败:', error)
      }
    }

    const deleteItem = async (id) => {
      if (confirm('确定要删除这个Newsletter吗？')) {
        try {
          await deleteNewsletter(id)
          await fetchNewsletters()
        } catch (error) {
          console.error('删除失败:', error)
        }
      }
    }

    onMounted(() => {
      fetchNewsletters()
    })

    return {
      items,
      loading,
      showCreateForm,
      editingItem,
      form,
      editItem,
      closeForm,
      submitForm,
      deleteItem
    }
  }
}
</script>

<style scoped>
.newsletter-component {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.item-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field {
  margin-bottom: 8px;
}

.field label {
  font-weight: bold;
  margin-right: 8px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}
</style>
