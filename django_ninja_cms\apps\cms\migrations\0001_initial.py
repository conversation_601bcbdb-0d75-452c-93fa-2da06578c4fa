# Generated by Django 5.2.4 on 2025-07-13 11:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        (
            "storage",
            "0003_storagefile_storage_backend_storagefile_storage_key_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                ("slug", models.Char<PERSON>ield(max_length=255, verbose_name="Slug")),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "is_active",
                    models.BooleanField(default=False, verbose_name="Is Active"),
                ),
                ("sort_order", models.IntegerField(verbose_name="Sort Order")),
                ("article_count", models.IntegerField(verbose_name="Article Count")),
                (
                    "seo_title",
                    models.CharField(max_length=255, verbose_name="Seo Title"),
                ),
                ("seo_description", models.TextField(verbose_name="Seo Description")),
                (
                    "image",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="category_images",
                        to="storage.storagefile",
                        verbose_name="Image",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="category_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cms.category",
                        verbose_name="Parent",
                    ),
                ),
            ],
            options={
                "verbose_name": "分类",
                "verbose_name_plural": "分类 列表",
                "db_table": "cms_category",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="Article",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("title", models.CharField(max_length=255, verbose_name="Title")),
                ("slug", models.CharField(max_length=255, verbose_name="Slug")),
                ("content", models.TextField(verbose_name="Content")),
                ("summary", models.CharField(max_length=255, verbose_name="Summary")),
                ("status", models.CharField(max_length=255, verbose_name="Status")),
                (
                    "is_featured",
                    models.BooleanField(default=False, verbose_name="Is Featured"),
                ),
                ("is_top", models.BooleanField(default=False, verbose_name="Is Top")),
                ("view_count", models.IntegerField(verbose_name="View Count")),
                ("like_count", models.IntegerField(verbose_name="Like Count")),
                ("comment_count", models.IntegerField(verbose_name="Comment Count")),
                ("reading_time", models.IntegerField(verbose_name="Reading Time")),
                ("published_at", models.DateTimeField(verbose_name="Published At")),
                (
                    "seo_title",
                    models.CharField(max_length=255, verbose_name="Seo Title"),
                ),
                ("seo_description", models.TextField(verbose_name="Seo Description")),
                (
                    "seo_keywords",
                    models.CharField(max_length=255, verbose_name="Seo Keywords"),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="authored_articles",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Author",
                    ),
                ),
                (
                    "featured_image",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="article_featured_images",
                        to="storage.storagefile",
                        verbose_name="Featured Image",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="article_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cms.category",
                        verbose_name="Category",
                    ),
                ),
            ],
            options={
                "verbose_name": "文章",
                "verbose_name_plural": "文章 列表",
                "db_table": "cms_article",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("content", models.TextField(verbose_name="Content")),
                ("status", models.CharField(max_length=255, verbose_name="Status")),
                (
                    "is_anonymous",
                    models.BooleanField(default=False, verbose_name="Is Anonymous"),
                ),
                (
                    "author_name",
                    models.CharField(max_length=255, verbose_name="Author Name"),
                ),
                (
                    "author_email",
                    models.CharField(max_length=255, verbose_name="Author Email"),
                ),
                (
                    "author_website",
                    models.CharField(max_length=255, verbose_name="Author Website"),
                ),
                (
                    "ip_address",
                    models.CharField(max_length=255, verbose_name="Ip Address"),
                ),
                (
                    "user_agent",
                    models.CharField(max_length=255, verbose_name="User Agent"),
                ),
                ("like_count", models.IntegerField(verbose_name="Like Count")),
                ("dislike_count", models.IntegerField(verbose_name="Dislike Count")),
                (
                    "article",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cms.article",
                        verbose_name="Article",
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="authored_comments",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Author",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comment_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cms.comment",
                        verbose_name="Parent",
                    ),
                ),
            ],
            options={
                "verbose_name": "评论",
                "verbose_name_plural": "评论 列表",
                "db_table": "cms_comment",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Newsletter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("email", models.CharField(max_length=255, verbose_name="Email")),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "is_active",
                    models.BooleanField(default=False, verbose_name="Is Active"),
                ),
                ("subscribed_at", models.DateTimeField(verbose_name="Subscribed At")),
                (
                    "unsubscribed_at",
                    models.DateTimeField(verbose_name="Unsubscribed At"),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="newsletter_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
            ],
            options={
                "verbose_name": "邮件订阅",
                "verbose_name_plural": "邮件订阅 列表",
                "db_table": "cms_newsletter",
                "ordering": ["-subscribed_at"],
            },
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                ("slug", models.CharField(max_length=255, verbose_name="Slug")),
                ("description", models.TextField(verbose_name="Description")),
                ("color", models.CharField(max_length=255, verbose_name="Color")),
                (
                    "is_active",
                    models.BooleanField(default=False, verbose_name="Is Active"),
                ),
                ("article_count", models.IntegerField(verbose_name="Article Count")),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tag_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
            ],
            options={
                "verbose_name": "标签",
                "verbose_name_plural": "标签 列表",
                "db_table": "cms_tag",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="article",
            name="tags",
            field=models.ManyToManyField(blank=True, to="cms.tag", verbose_name="Tags"),
        ),
        migrations.CreateModel(
            name="ArticleLike",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("is_like", models.BooleanField(default=False, verbose_name="Is Like")),
                (
                    "article",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cms.article",
                        verbose_name="Article",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="articlelike_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="article_likes",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "文章点赞",
                "verbose_name_plural": "文章点赞 列表",
                "db_table": "cms_articlelike",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["owner"], name="cms_article_owner_i_755724_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="cms_article_created_7cc47d_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ArticleView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "ip_address",
                    models.CharField(max_length=255, verbose_name="Ip Address"),
                ),
                (
                    "user_agent",
                    models.CharField(max_length=255, verbose_name="User Agent"),
                ),
                ("referrer", models.CharField(max_length=255, verbose_name="Referrer")),
                ("view_date", models.DateField(verbose_name="View Date")),
                (
                    "article",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cms.article",
                        verbose_name="Article",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="articleview_set",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="所有者",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="article_views",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "文章浏览记录",
                "verbose_name_plural": "文章浏览记录 列表",
                "db_table": "cms_articleview",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["owner"], name="cms_article_owner_i_7a3bcd_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="cms_article_created_8e9d46_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="category",
            index=models.Index(fields=["owner"], name="cms_categor_owner_i_66f84d_idx"),
        ),
        migrations.AddIndex(
            model_name="category",
            index=models.Index(
                fields=["created_at"], name="cms_categor_created_9a3c4a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(fields=["owner"], name="cms_comment_owner_i_70bcba_idx"),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["created_at"], name="cms_comment_created_1f9115_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="newsletter",
            index=models.Index(fields=["owner"], name="cms_newslet_owner_i_ee6a50_idx"),
        ),
        migrations.AddIndex(
            model_name="newsletter",
            index=models.Index(
                fields=["created_at"], name="cms_newslet_created_255abd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tag",
            index=models.Index(fields=["owner"], name="cms_tag_owner_i_150004_idx"),
        ),
        migrations.AddIndex(
            model_name="tag",
            index=models.Index(
                fields=["created_at"], name="cms_tag_created_3628d1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(fields=["owner"], name="cms_article_owner_i_4641ea_idx"),
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(
                fields=["created_at"], name="cms_article_created_a655a4_idx"
            ),
        ),
    ]
