"""
Cms API 接口
"""
from typing import List
from ninja import Router
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from apps.permissions.decorators import require_permissions

from .models import Article, Category, Tag, Comment, ArticleView, ArticleLike, Newsletter
from .schemas import ArticleSchema, ArticleCreateSchema, ArticleUpdateSchema, CategorySchema, CategoryCreateSchema, CategoryUpdateSchema, TagSchema, TagCreateSchema, TagUpdateSchema, CommentSchema, CommentCreateSchema, CommentUpdateSchema, ArticleViewSchema, ArticleViewCreateSchema, ArticleViewUpdateSchema, ArticleLikeSchema, ArticleLikeCreateSchema, ArticleLikeUpdateSchema, NewsletterSchema, NewsletterCreateSchema, NewsletterUpdateSchema
from .services import ArticleService, CategoryService, TagService, CommentService, ArticleViewService, ArticleLikeService, NewsletterService

router = Router(tags=["Cms"])

# 导出路由器
cms_router = router

# Article API 端点
@router.get("/article/", response=List[ArticleSchema])
@login_required
@require_permissions('cms.article.view')
def list_article(request):
    """Article 列表"""
    return ArticleService.get_user_items(request.user)


@router.get("/article/{item_id}/", response=ArticleSchema)
@login_required
@require_permissions('cms.article.view')
def get_article(request, item_id: int):
    """Article 详情"""
    return ArticleService.get_user_item(request.user, item_id)


@router.post("/article/", response=ArticleSchema)
@login_required
@require_permissions('cms.article.add')
def create_article(request, data: ArticleCreateSchema):
    """创建 Article"""
    return ArticleService.create_item(request.user, data.dict())


@router.put("/article/{item_id}/", response=ArticleSchema)
@login_required
@require_permissions('cms.article.change')
def update_article(request, item_id: int, data: ArticleUpdateSchema):
    """更新 Article"""
    return ArticleService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/article/{item_id}/")
@login_required
@require_permissions('cms.article.delete')
def delete_article(request, item_id: int):
    """删除 Article"""
    ArticleService.delete_item(request.user, item_id)
    return {"message": "Article 删除成功"}

# Category API 端点
@router.get("/category/", response=List[CategorySchema])
@login_required
@require_permissions('cms.category.view')
def list_category(request):
    """Category 列表"""
    return CategoryService.get_user_items(request.user)


@router.get("/category/{item_id}/", response=CategorySchema)
@login_required
@require_permissions('cms.category.view')
def get_category(request, item_id: int):
    """Category 详情"""
    return CategoryService.get_user_item(request.user, item_id)


@router.post("/category/", response=CategorySchema)
@login_required
@require_permissions('cms.category.add')
def create_category(request, data: CategoryCreateSchema):
    """创建 Category"""
    return CategoryService.create_item(request.user, data.dict())


@router.put("/category/{item_id}/", response=CategorySchema)
@login_required
@require_permissions('cms.category.change')
def update_category(request, item_id: int, data: CategoryUpdateSchema):
    """更新 Category"""
    return CategoryService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/category/{item_id}/")
@login_required
@require_permissions('cms.category.delete')
def delete_category(request, item_id: int):
    """删除 Category"""
    CategoryService.delete_item(request.user, item_id)
    return {"message": "Category 删除成功"}

# Tag API 端点
@router.get("/tag/", response=List[TagSchema])
@login_required
@require_permissions('cms.tag.view')
def list_tag(request):
    """Tag 列表"""
    return TagService.get_user_items(request.user)


@router.get("/tag/{item_id}/", response=TagSchema)
@login_required
@require_permissions('cms.tag.view')
def get_tag(request, item_id: int):
    """Tag 详情"""
    return TagService.get_user_item(request.user, item_id)


@router.post("/tag/", response=TagSchema)
@login_required
@require_permissions('cms.tag.add')
def create_tag(request, data: TagCreateSchema):
    """创建 Tag"""
    return TagService.create_item(request.user, data.dict())


@router.put("/tag/{item_id}/", response=TagSchema)
@login_required
@require_permissions('cms.tag.change')
def update_tag(request, item_id: int, data: TagUpdateSchema):
    """更新 Tag"""
    return TagService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/tag/{item_id}/")
@login_required
@require_permissions('cms.tag.delete')
def delete_tag(request, item_id: int):
    """删除 Tag"""
    TagService.delete_item(request.user, item_id)
    return {"message": "Tag 删除成功"}

# Comment API 端点
@router.get("/comment/", response=List[CommentSchema])
@login_required
@require_permissions('cms.comment.view')
def list_comment(request):
    """Comment 列表"""
    return CommentService.get_user_items(request.user)


@router.get("/comment/{item_id}/", response=CommentSchema)
@login_required
@require_permissions('cms.comment.view')
def get_comment(request, item_id: int):
    """Comment 详情"""
    return CommentService.get_user_item(request.user, item_id)


@router.post("/comment/", response=CommentSchema)
@login_required
@require_permissions('cms.comment.add')
def create_comment(request, data: CommentCreateSchema):
    """创建 Comment"""
    return CommentService.create_item(request.user, data.dict())


@router.put("/comment/{item_id}/", response=CommentSchema)
@login_required
@require_permissions('cms.comment.change')
def update_comment(request, item_id: int, data: CommentUpdateSchema):
    """更新 Comment"""
    return CommentService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/comment/{item_id}/")
@login_required
@require_permissions('cms.comment.delete')
def delete_comment(request, item_id: int):
    """删除 Comment"""
    CommentService.delete_item(request.user, item_id)
    return {"message": "Comment 删除成功"}

# ArticleView API 端点
@router.get("/articleview/", response=List[ArticleViewSchema])
@login_required
@require_permissions('cms.articleview.view')
def list_articleview(request):
    """ArticleView 列表"""
    return ArticleViewService.get_user_items(request.user)


@router.get("/articleview/{item_id}/", response=ArticleViewSchema)
@login_required
@require_permissions('cms.articleview.view')
def get_articleview(request, item_id: int):
    """ArticleView 详情"""
    return ArticleViewService.get_user_item(request.user, item_id)


@router.post("/articleview/", response=ArticleViewSchema)
@login_required
@require_permissions('cms.articleview.add')
def create_articleview(request, data: ArticleViewCreateSchema):
    """创建 ArticleView"""
    return ArticleViewService.create_item(request.user, data.dict())


@router.put("/articleview/{item_id}/", response=ArticleViewSchema)
@login_required
@require_permissions('cms.articleview.change')
def update_articleview(request, item_id: int, data: ArticleViewUpdateSchema):
    """更新 ArticleView"""
    return ArticleViewService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/articleview/{item_id}/")
@login_required
@require_permissions('cms.articleview.delete')
def delete_articleview(request, item_id: int):
    """删除 ArticleView"""
    ArticleViewService.delete_item(request.user, item_id)
    return {"message": "ArticleView 删除成功"}

# ArticleLike API 端点
@router.get("/articlelike/", response=List[ArticleLikeSchema])
@login_required
@require_permissions('cms.articlelike.view')
def list_articlelike(request):
    """ArticleLike 列表"""
    return ArticleLikeService.get_user_items(request.user)


@router.get("/articlelike/{item_id}/", response=ArticleLikeSchema)
@login_required
@require_permissions('cms.articlelike.view')
def get_articlelike(request, item_id: int):
    """ArticleLike 详情"""
    return ArticleLikeService.get_user_item(request.user, item_id)


@router.post("/articlelike/", response=ArticleLikeSchema)
@login_required
@require_permissions('cms.articlelike.add')
def create_articlelike(request, data: ArticleLikeCreateSchema):
    """创建 ArticleLike"""
    return ArticleLikeService.create_item(request.user, data.dict())


@router.put("/articlelike/{item_id}/", response=ArticleLikeSchema)
@login_required
@require_permissions('cms.articlelike.change')
def update_articlelike(request, item_id: int, data: ArticleLikeUpdateSchema):
    """更新 ArticleLike"""
    return ArticleLikeService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/articlelike/{item_id}/")
@login_required
@require_permissions('cms.articlelike.delete')
def delete_articlelike(request, item_id: int):
    """删除 ArticleLike"""
    ArticleLikeService.delete_item(request.user, item_id)
    return {"message": "ArticleLike 删除成功"}

# Newsletter API 端点
@router.get("/newsletter/", response=List[NewsletterSchema])
@login_required
@require_permissions('cms.newsletter.view')
def list_newsletter(request):
    """Newsletter 列表"""
    return NewsletterService.get_user_items(request.user)


@router.get("/newsletter/{item_id}/", response=NewsletterSchema)
@login_required
@require_permissions('cms.newsletter.view')
def get_newsletter(request, item_id: int):
    """Newsletter 详情"""
    return NewsletterService.get_user_item(request.user, item_id)


@router.post("/newsletter/", response=NewsletterSchema)
@login_required
@require_permissions('cms.newsletter.add')
def create_newsletter(request, data: NewsletterCreateSchema):
    """创建 Newsletter"""
    return NewsletterService.create_item(request.user, data.dict())


@router.put("/newsletter/{item_id}/", response=NewsletterSchema)
@login_required
@require_permissions('cms.newsletter.change')
def update_newsletter(request, item_id: int, data: NewsletterUpdateSchema):
    """更新 Newsletter"""
    return NewsletterService.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/newsletter/{item_id}/")
@login_required
@require_permissions('cms.newsletter.delete')
def delete_newsletter(request, item_id: int):
    """删除 Newsletter"""
    NewsletterService.delete_item(request.user, item_id)
    return {"message": "Newsletter 删除成功"}

