"""
Cms 应用数据模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()


class Article(BaseModel):
    """文章 模型"""

    title = models.CharField(max_length=255, verbose_name=_('Title'))
    slug = models.CharField(max_length=255, verbose_name=_('Slug'))
    content = models.TextField(verbose_name=_('Content'))
    summary = models.CharField(max_length=255, verbose_name=_('Summary'))
    status = models.CharField(max_length=255, verbose_name=_('Status'))
    is_featured = models.BooleanField(default=False, verbose_name=_('Is Featured'))
    is_top = models.BooleanField(default=False, verbose_name=_('Is Top'))
    view_count = models.IntegerField(verbose_name=_('View Count'))
    like_count = models.IntegerField(verbose_name=_('Like Count'))
    comment_count = models.IntegerField(verbose_name=_('Comment Count'))
    reading_time = models.IntegerField(verbose_name=_('Reading Time'))
    published_at = models.DateTimeField(verbose_name=_('Published At'))
    author = models.ForeignKey("users.User", on_delete=models.CASCADE, related_name='authored_articles', verbose_name=_('Author'))
    category = models.ForeignKey("Category", on_delete=models.CASCADE, verbose_name=_('Category'))
    tags = models.ManyToManyField("Tag", blank=True, verbose_name=_('Tags'))
    featured_image = models.ForeignKey("storage.StorageFile", on_delete=models.SET_NULL, null=True, blank=True, related_name='article_featured_images', verbose_name=_('Featured Image'))
    seo_title = models.CharField(max_length=255, verbose_name=_('Seo Title'))
    seo_description = models.TextField(verbose_name=_('Seo Description'))
    seo_keywords = models.CharField(max_length=255, verbose_name=_('Seo Keywords'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='article_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('文章')
        verbose_name_plural = _('文章 列表')
        db_table = 'cms_article'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return self.title

class Category(BaseModel):
    """分类 模型"""

    name = models.CharField(max_length=255, verbose_name=_('Name'))
    slug = models.CharField(max_length=255, verbose_name=_('Slug'))
    description = models.TextField(verbose_name=_('Description'))
    parent = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True, verbose_name=_('Parent'))
    image = models.ForeignKey("storage.StorageFile", on_delete=models.SET_NULL, null=True, blank=True, related_name='category_images', verbose_name=_('Image'))
    is_active = models.BooleanField(default=False, verbose_name=_('Is Active'))
    sort_order = models.IntegerField(verbose_name=_('Sort Order'))
    article_count = models.IntegerField(verbose_name=_('Article Count'))
    seo_title = models.CharField(max_length=255, verbose_name=_('Seo Title'))
    seo_description = models.TextField(verbose_name=_('Seo Description'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='category_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('分类')
        verbose_name_plural = _('分类 列表')
        db_table = 'cms_category'
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return self.name

class Tag(BaseModel):
    """标签 模型"""

    name = models.CharField(max_length=255, verbose_name=_('Name'))
    slug = models.CharField(max_length=255, verbose_name=_('Slug'))
    description = models.TextField(verbose_name=_('Description'))
    color = models.CharField(max_length=255, verbose_name=_('Color'))
    is_active = models.BooleanField(default=False, verbose_name=_('Is Active'))
    article_count = models.IntegerField(verbose_name=_('Article Count'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='tag_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('标签')
        verbose_name_plural = _('标签 列表')
        db_table = 'cms_tag'
        ordering = ['name']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return self.name

class Comment(BaseModel):
    """评论 模型"""

    content = models.TextField(verbose_name=_('Content'))
    status = models.CharField(max_length=255, verbose_name=_('Status'))
    is_anonymous = models.BooleanField(default=False, verbose_name=_('Is Anonymous'))
    author_name = models.CharField(max_length=255, verbose_name=_('Author Name'))
    author_email = models.CharField(max_length=255, verbose_name=_('Author Email'))
    author_website = models.CharField(max_length=255, verbose_name=_('Author Website'))
    ip_address = models.CharField(max_length=255, verbose_name=_('Ip Address'))
    user_agent = models.CharField(max_length=255, verbose_name=_('User Agent'))
    like_count = models.IntegerField(verbose_name=_('Like Count'))
    dislike_count = models.IntegerField(verbose_name=_('Dislike Count'))
    author = models.ForeignKey("users.User", on_delete=models.CASCADE, related_name='authored_comments', verbose_name=_('Author'))
    article = models.ForeignKey("Article", on_delete=models.CASCADE, verbose_name=_('Article'))
    parent = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True, verbose_name=_('Parent'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comment_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('评论')
        verbose_name_plural = _('评论 列表')
        db_table = 'cms_comment'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return f'Comment #{self.id}'

class ArticleView(BaseModel):
    """文章浏览记录 模型"""

    article = models.ForeignKey("Article", on_delete=models.CASCADE, verbose_name=_('Article'))
    user = models.ForeignKey("users.User", on_delete=models.CASCADE, related_name='article_views', verbose_name=_('User'))
    ip_address = models.CharField(max_length=255, verbose_name=_('Ip Address'))
    user_agent = models.CharField(max_length=255, verbose_name=_('User Agent'))
    referrer = models.CharField(max_length=255, verbose_name=_('Referrer'))
    view_date = models.DateField(verbose_name=_('View Date'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='articleview_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('文章浏览记录')
        verbose_name_plural = _('文章浏览记录 列表')
        db_table = 'cms_articleview'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return f'ArticleView #{self.id}'

class ArticleLike(BaseModel):
    """文章点赞 模型"""

    article = models.ForeignKey("Article", on_delete=models.CASCADE, verbose_name=_('Article'))
    user = models.ForeignKey("users.User", on_delete=models.CASCADE, related_name='article_likes', verbose_name=_('User'))
    is_like = models.BooleanField(default=False, verbose_name=_('Is Like'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='articlelike_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('文章点赞')
        verbose_name_plural = _('文章点赞 列表')
        db_table = 'cms_articlelike'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return f'ArticleLike #{self.id}'

class Newsletter(BaseModel):
    """邮件订阅 模型"""

    email = models.CharField(max_length=255, verbose_name=_('Email'))
    name = models.CharField(max_length=255, verbose_name=_('Name'))
    is_active = models.BooleanField(default=False, verbose_name=_('Is Active'))
    subscribed_at = models.DateTimeField(verbose_name=_('Subscribed At'))
    unsubscribed_at = models.DateTimeField(null=True, blank=True, verbose_name=_('Unsubscribed At'))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='newsletter_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('邮件订阅')
        verbose_name_plural = _('邮件订阅 列表')
        db_table = 'cms_newsletter'
        ordering = ['-subscribed_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]


    def __str__(self):
        return self.name

