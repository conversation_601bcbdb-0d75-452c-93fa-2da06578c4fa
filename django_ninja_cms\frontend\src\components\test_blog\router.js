/**
 * Test_Blog 路由配置
 */
import { createRouter, createWebHistory } from 'vue-router'

import CategoryComponent from './Category.vue'
import PostComponent from './Post.vue'
import TagComponent from './Tag.vue'

const routes = [
  {
    path: '/category',
    name: 'Category',
    component: CategoryComponent
  },
  {
    path: '/post',
    name: 'Post',
    component: PostComponent
  },
  {
    path: '/tag',
    name: 'Tag',
    component: TagComponent
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
