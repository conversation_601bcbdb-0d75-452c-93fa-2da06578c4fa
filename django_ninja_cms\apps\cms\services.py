"""
Cms 业务逻辑服务
"""
from typing import List, Dict, Any
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.core.exceptions import PermissionDenied

from .models import Article, Category, Tag, Comment, ArticleView, ArticleLike, Newsletter

User = get_user_model()

class ArticleService:
    """Article 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[Article]:
        """获取用户的 Article 列表"""
        return Article.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> Article:
        """获取用户的单个 Article"""
        return get_object_or_404(Article, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> Article:
        """创建 Article"""
        data['owner'] = user
        return Article.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> Article:
        """更新 Article"""
        item = ArticleService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 Article"""
        item = ArticleService.get_user_item(user, item_id)
        item.delete()

class CategoryService:
    """Category 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[Category]:
        """获取用户的 Category 列表"""
        return Category.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> Category:
        """获取用户的单个 Category"""
        return get_object_or_404(Category, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> Category:
        """创建 Category"""
        data['owner'] = user
        return Category.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> Category:
        """更新 Category"""
        item = CategoryService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 Category"""
        item = CategoryService.get_user_item(user, item_id)
        item.delete()

class TagService:
    """Tag 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[Tag]:
        """获取用户的 Tag 列表"""
        return Tag.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> Tag:
        """获取用户的单个 Tag"""
        return get_object_or_404(Tag, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> Tag:
        """创建 Tag"""
        data['owner'] = user
        return Tag.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> Tag:
        """更新 Tag"""
        item = TagService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 Tag"""
        item = TagService.get_user_item(user, item_id)
        item.delete()

class CommentService:
    """Comment 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[Comment]:
        """获取用户的 Comment 列表"""
        return Comment.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> Comment:
        """获取用户的单个 Comment"""
        return get_object_or_404(Comment, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> Comment:
        """创建 Comment"""
        data['owner'] = user
        return Comment.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> Comment:
        """更新 Comment"""
        item = CommentService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 Comment"""
        item = CommentService.get_user_item(user, item_id)
        item.delete()

class ArticleViewService:
    """ArticleView 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[ArticleView]:
        """获取用户的 ArticleView 列表"""
        return ArticleView.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> ArticleView:
        """获取用户的单个 ArticleView"""
        return get_object_or_404(ArticleView, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> ArticleView:
        """创建 ArticleView"""
        data['owner'] = user
        return ArticleView.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> ArticleView:
        """更新 ArticleView"""
        item = ArticleViewService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 ArticleView"""
        item = ArticleViewService.get_user_item(user, item_id)
        item.delete()

class ArticleLikeService:
    """ArticleLike 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[ArticleLike]:
        """获取用户的 ArticleLike 列表"""
        return ArticleLike.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> ArticleLike:
        """获取用户的单个 ArticleLike"""
        return get_object_or_404(ArticleLike, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> ArticleLike:
        """创建 ArticleLike"""
        data['owner'] = user
        return ArticleLike.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> ArticleLike:
        """更新 ArticleLike"""
        item = ArticleLikeService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 ArticleLike"""
        item = ArticleLikeService.get_user_item(user, item_id)
        item.delete()

class NewsletterService:
    """Newsletter 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[Newsletter]:
        """获取用户的 Newsletter 列表"""
        return Newsletter.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> Newsletter:
        """获取用户的单个 Newsletter"""
        return get_object_or_404(Newsletter, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> Newsletter:
        """创建 Newsletter"""
        data['owner'] = user
        return Newsletter.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> Newsletter:
        """更新 Newsletter"""
        item = NewsletterService.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 Newsletter"""
        item = NewsletterService.get_user_item(user, item_id)
        item.delete()

