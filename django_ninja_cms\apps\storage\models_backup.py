"""
File storage models.
"""
import os
import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator
from apps.core.models import BaseModel

User = get_user_model()


def upload_to_path(instance, filename):
    """
    Generate upload path for files.
    """
    # Get file extension
    ext = filename.split('.')[-1]
    # Generate unique filename
    filename = f"{uuid.uuid4()}.{ext}"
    # Return path: uploads/year/month/day/filename
    from datetime import datetime
    now = datetime.now()
    return f"uploads/{now.year}/{now.month:02d}/{now.day:02d}/{filename}"


class FileUpload(BaseModel):
    """
    Model to track uploaded files.
    """
    STORAGE_CHOICES = [
        ('local', _('Local Storage')),
        ('s3', _('Amazon S3')),
        ('gcs', _('Google Cloud Storage')),
        ('azure', _('Azure Blob Storage')),
    ]
    
    FILE_TYPE_CHOICES = [
        ('image', _('Image')),
        ('document', _('Document')),
        ('video', _('Video')),
        ('audio', _('Audio')),
        ('archive', _('Archive')),
        ('other', _('Other')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='uploaded_files'
    )
    
    # File information
    original_name = models.CharField(_('original filename'), max_length=255)
    file = models.FileField(_('file'), upload_to=upload_to_path)
    file_size = models.PositiveIntegerField(_('file size (bytes)'))
    file_type = models.CharField(_('file type'), max_length=20, choices=FILE_TYPE_CHOICES)
    mime_type = models.CharField(_('MIME type'), max_length=100)
    
    # Storage information
    storage_backend = models.CharField(
        _('storage backend'),
        max_length=20,
        choices=STORAGE_CHOICES,
        default='local'
    )
    storage_path = models.CharField(_('storage path'), max_length=500)
    
    # Metadata
    metadata = models.JSONField(_('metadata'), default=dict, blank=True)
    
    # Access control
    is_public = models.BooleanField(_('is public'), default=False)
    is_temporary = models.BooleanField(_('is temporary'), default=False)
    expires_at = models.DateTimeField(_('expires at'), null=True, blank=True)
    
    # Image-specific fields
    width = models.PositiveIntegerField(_('width'), null=True, blank=True)
    height = models.PositiveIntegerField(_('height'), null=True, blank=True)
    
    # Download tracking
    download_count = models.PositiveIntegerField(_('download count'), default=0)
    last_accessed = models.DateTimeField(_('last accessed'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('File Upload')
        verbose_name_plural = _('File Uploads')
        db_table = 'storage_file_uploads'
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['file_type']),
            models.Index(fields=['is_public']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.original_name} ({self.user.email})"
    
    @property
    def file_extension(self):
        """Get file extension."""
        return os.path.splitext(self.original_name)[1].lower()
    
    @property
    def is_image(self):
        """Check if file is an image."""
        return self.file_type == 'image'
    
    @property
    def is_expired(self):
        """Check if file is expired."""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    def get_download_url(self):
        """Get download URL for the file."""
        if self.storage_backend == 'local':
            return self.file.url
        else:
            # For cloud storage, generate signed URL
            from .services import StorageService
            return StorageService.get_download_url(self)
    
    def increment_download_count(self):
        """Increment download count."""
        from django.utils import timezone
        self.download_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['download_count', 'last_accessed'])


class FileShare(BaseModel):
    """
    Model for sharing files with specific users or publicly.
    """
    PERMISSION_CHOICES = [
        ('view', _('View Only')),
        ('download', _('Download')),
        ('edit', _('Edit')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.ForeignKey(
        FileUpload,
        on_delete=models.CASCADE,
        related_name='shares'
    )
    shared_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='shared_files'
    )
    shared_with = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_files',
        null=True,
        blank=True
    )
    
    # Share settings
    permission = models.CharField(_('permission'), max_length=20, choices=PERMISSION_CHOICES)
    is_public = models.BooleanField(_('is public'), default=False)
    share_token = models.CharField(_('share token'), max_length=64, unique=True)
    
    # Access control
    password_protected = models.BooleanField(_('password protected'), default=False)
    password_hash = models.CharField(_('password hash'), max_length=128, blank=True)
    
    # Expiration
    expires_at = models.DateTimeField(_('expires at'), null=True, blank=True)
    max_downloads = models.PositiveIntegerField(_('max downloads'), null=True, blank=True)
    download_count = models.PositiveIntegerField(_('download count'), default=0)
    
    # Tracking
    last_accessed = models.DateTimeField(_('last accessed'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('File Share')
        verbose_name_plural = _('File Shares')
        db_table = 'storage_file_shares'
        indexes = [
            models.Index(fields=['share_token']),
            models.Index(fields=['shared_by']),
            models.Index(fields=['shared_with']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Share: {self.file.original_name} by {self.shared_by.email}"
    
    @property
    def is_expired(self):
        """Check if share is expired."""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    @property
    def is_download_limit_reached(self):
        """Check if download limit is reached."""
        if self.max_downloads:
            return self.download_count >= self.max_downloads
        return False
    
    def can_access(self, user=None, password=None):
        """Check if user can access this share."""
        # Check expiration
        if self.is_expired:
            return False
        
        # Check download limit
        if self.is_download_limit_reached:
            return False
        
        # Check password protection
        if self.password_protected and password:
            from django.contrib.auth.hashers import check_password
            if not check_password(password, self.password_hash):
                return False
        
        # Check user access
        if not self.is_public and user:
            if self.shared_with and self.shared_with != user:
                return False
        
        return True
    
    def increment_download_count(self):
        """Increment download count."""
        from django.utils import timezone
        self.download_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['download_count', 'last_accessed'])