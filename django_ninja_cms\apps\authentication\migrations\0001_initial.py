# Generated by Django 5.2.4 on 2025-07-12 13:56

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="LoginAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254, verbose_name="email")),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP address")),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
                ("success", models.BooleanField(verbose_name="success")),
                (
                    "failure_reason",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("invalid_credentials", "Invalid credentials"),
                            ("account_disabled", "Account disabled"),
                            ("account_locked", "Account locked"),
                            ("too_many_attempts", "Too many attempts"),
                        ],
                        max_length=100,
                        verbose_name="failure reason",
                    ),
                ),
                (
                    "attempted_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="attempted at"
                    ),
                ),
            ],
            options={
                "verbose_name": "Login Attempt",
                "verbose_name_plural": "Login Attempts",
                "db_table": "auth_login_attempts",
                "indexes": [
                    models.Index(
                        fields=["email", "attempted_at"],
                        name="auth_login__email_c4fbee_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "attempted_at"],
                        name="auth_login__ip_addr_0fb781_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "token",
                    models.CharField(max_length=255, unique=True, verbose_name="token"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                ("is_used", models.BooleanField(default=False, verbose_name="is used")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Token",
                "verbose_name_plural": "Email Verification Tokens",
                "db_table": "auth_email_verification_tokens",
                "indexes": [
                    models.Index(fields=["token"], name="auth_email__token_247a2c_idx"),
                    models.Index(
                        fields=["expires_at"], name="auth_email__expires_8a24ed_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "token",
                    models.CharField(max_length=255, unique=True, verbose_name="token"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                ("is_used", models.BooleanField(default=False, verbose_name="is used")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Password Reset Token",
                "verbose_name_plural": "Password Reset Tokens",
                "db_table": "auth_password_reset_tokens",
                "indexes": [
                    models.Index(fields=["token"], name="auth_passwo_token_076a22_idx"),
                    models.Index(
                        fields=["expires_at"], name="auth_passwo_expires_ca8703_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RefreshToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.TextField(verbose_name="refresh token")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                (
                    "is_revoked",
                    models.BooleanField(default=False, verbose_name="is revoked"),
                ),
                (
                    "device_name",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="device name"
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP address"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="refresh_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Refresh Token",
                "verbose_name_plural": "Refresh Tokens",
                "db_table": "auth_refresh_tokens",
                "indexes": [
                    models.Index(
                        fields=["user", "is_revoked"],
                        name="auth_refres_user_id_6c5f7d_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="auth_refres_expires_4ad26b_idx"
                    ),
                ],
            },
        ),
    ]
