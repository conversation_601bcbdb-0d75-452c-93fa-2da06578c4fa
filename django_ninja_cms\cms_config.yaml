app_name: cms
models:
  Article:
    fields:
      title: str
      slug: str
      content: text
      summary: str
      status: choices:draft,published,archived,deleted
      is_featured: bool
      is_top: bool
      view_count: int
      like_count: int
      comment_count: int
      reading_time: int
      published_at: datetime
      author: fk:User
      category: fk:Category
      tags: m2m:Tag
      featured_image: fk:storage.StorageFile
      seo_title: str
      seo_description: text
      seo_keywords: str
    options:
      ordering: ['-created_at']
      verbose_name: '文章'
      verbose_name_plural: '文章'

  Category:
    fields:
      name: str
      slug: str
      description: text
      parent: fk:self
      image: fk:storage.StorageFile
      is_active: bool
      sort_order: int
      article_count: int
      seo_title: str
      seo_description: text
    options:
      ordering: ['sort_order', 'name']
      verbose_name: '分类'
      verbose_name_plural: '分类'

  Tag:
    fields:
      name: str
      slug: str
      description: text
      color: str
      is_active: bool
      article_count: int
    options:
      ordering: ['name']
      verbose_name: '标签'
      verbose_name_plural: '标签'

  Comment:
    fields:
      content: text
      status: choices:pending,approved,rejected,spam
      is_anonymous: bool
      author_name: str
      author_email: str
      author_website: str
      ip_address: str
      user_agent: str
      like_count: int
      dislike_count: int
      author: fk:User
      article: fk:Article
      parent: fk:self
    options:
      ordering: ['-created_at']
      verbose_name: '评论'
      verbose_name_plural: '评论'

  ArticleView:
    fields:
      article: fk:Article
      user: fk:User
      ip_address: str
      user_agent: str
      referrer: str
      view_date: date
    options:
      ordering: ['-created_at']
      verbose_name: '文章浏览记录'
      verbose_name_plural: '文章浏览记录'

  ArticleLike:
    fields:
      article: fk:Article
      user: fk:User
      is_like: bool
    options:
      ordering: ['-created_at']
      verbose_name: '文章点赞'
      verbose_name_plural: '文章点赞'

  Newsletter:
    fields:
      email: str
      name: str
      is_active: bool
      subscribed_at: datetime
      unsubscribed_at: datetime
    options:
      ordering: ['-subscribed_at']
      verbose_name: '邮件订阅'
      verbose_name_plural: '邮件订阅'