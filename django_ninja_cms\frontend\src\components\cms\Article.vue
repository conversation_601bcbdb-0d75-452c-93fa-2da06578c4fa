<template>
  <div class="article-component">
    <div class="header">
      <h2>Article 管理</h2>
      <button @click="showCreateForm = true" class="btn btn-primary">
        添加 Article
      </button>
    </div>

    <!-- 列表 -->
    <div class="list-container">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else>
        <div v-for="item in items" :key="item.id" class="item-card">
          <div class="item-content">
            <div class="field">
              <label>Title:</label>
              <span>{{ item.title }}</span>
            </div>
            <div class="field">
              <label>Slug:</label>
              <span>{{ item.slug }}</span>
            </div>
            <div class="field">
              <label>Content:</label>
              <span>{{ item.content }}</span>
            </div>
          </div>
          <div class="item-actions">
            <button @click="editItem(item)" class="btn btn-sm btn-secondary">编辑</button>
            <button @click="deleteItem(item.id)" class="btn btn-sm btn-danger">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑表单 -->
    <div v-if="showCreateForm || editingItem" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingItem ? '编辑' : '创建' }} Article</h3>
          <button @click="closeForm" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="submitForm" class="form">
          <div class="form-group">
            <label for="title">Title:</label>
            <input type="text"
              id="title"
              v-model="form.title"
              required
            />
          </div>
          <div class="form-group">
            <label for="slug">Slug:</label>
            <input type="text"
              id="slug"
              v-model="form.slug"
              required
            />
          </div>
          <div class="form-group">
            <label for="content">Content:</label>
            <textarea
              id="content"
              v-model="form.content"
              required
            />
          </div>
          <div class="form-group">
            <label for="summary">Summary:</label>
            <input type="text"
              id="summary"
              v-model="form.summary"
              required
            />
          </div>
          <div class="form-group">
            <label for="status">Status:</label>
            <input type="text"
              id="status"
              v-model="form.status"
              required
            />
          </div>
          <div class="form-group">
            <label for="is_featured">Is Featured:</label>
            <input type="checkbox"
              id="is_featured"
              v-model="form.is_featured"
              required
            />
          </div>
          <div class="form-group">
            <label for="is_top">Is Top:</label>
            <input type="checkbox"
              id="is_top"
              v-model="form.is_top"
              required
            />
          </div>
          <div class="form-group">
            <label for="view_count">View Count:</label>
            <input type="number"
              id="view_count"
              v-model="form.view_count"
              required
            />
          </div>
          <div class="form-group">
            <label for="like_count">Like Count:</label>
            <input type="number"
              id="like_count"
              v-model="form.like_count"
              required
            />
          </div>
          <div class="form-group">
            <label for="comment_count">Comment Count:</label>
            <input type="number"
              id="comment_count"
              v-model="form.comment_count"
              required
            />
          </div>
          <div class="form-group">
            <label for="reading_time">Reading Time:</label>
            <input type="number"
              id="reading_time"
              v-model="form.reading_time"
              required
            />
          </div>
          <div class="form-group">
            <label for="published_at">Published At:</label>
            <input type="datetime-local"
              id="published_at"
              v-model="form.published_at"
              required
            />
          </div>
          <div class="form-group">
            <label for="author">Author:</label>
            <select
              id="author"
              v-model="form.author"
              required
            />
          </div>
          <div class="form-group">
            <label for="category">Category:</label>
            <select
              id="category"
              v-model="form.category"
              required
            />
          </div>
          <div class="form-group">
            <label for="tags">Tags:</label>
            <select
              id="tags"
              v-model="form.tags"
              required
            />
          </div>
          <div class="form-group">
            <label for="featured_image">Featured Image:</label>
            <select
              id="featured_image"
              v-model="form.featured_image"
              required
            />
          </div>
          <div class="form-group">
            <label for="seo_title">Seo Title:</label>
            <input type="text"
              id="seo_title"
              v-model="form.seo_title"
              required
            />
          </div>
          <div class="form-group">
            <label for="seo_description">Seo Description:</label>
            <textarea
              id="seo_description"
              v-model="form.seo_description"
              required
            />
          </div>
          <div class="form-group">
            <label for="seo_keywords">Seo Keywords:</label>
            <input type="text"
              id="seo_keywords"
              v-model="form.seo_keywords"
              required
            />
          </div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              {{ editingItem ? '更新' : '创建' }}
            </button>
            <button type="button" @click="closeForm" class="btn btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useArticleAPI } from '../composables/useArticleAPI'

export default {
  name: 'ArticleComponent',
  setup() {
    const { items, loading, createArticle, updateArticle, deleteArticle, fetchArticles } = useArticleAPI()

    const showCreateForm = ref(false)
    const editingItem = ref(null)
    const form = ref({
      title: '',
      slug: '',
      content: '',
      summary: '',
      status: '',
      is_featured: false,
      is_top: false,
      view_count: 0,
      like_count: 0,
      comment_count: 0,
      reading_time: 0,
      published_at: '',
      author: '',
      category: '',
      tags: [],
      featured_image: '',
      seo_title: '',
      seo_description: '',
      seo_keywords: '',
    })

    const editItem = (item) => {
      editingItem.value = item
      form.value = { ...item }
    }

    const closeForm = () => {
      showCreateForm.value = false
      editingItem.value = null
      resetForm()
    }

    const resetForm = () => {
      form.value = {
        title: '',
        slug: '',
        content: '',
        summary: '',
        status: '',
        is_featured: false,
        is_top: false,
        view_count: 0,
        like_count: 0,
        comment_count: 0,
        reading_time: 0,
        published_at: '',
        author: '',
        category: '',
        tags: [],
        featured_image: '',
        seo_title: '',
        seo_description: '',
        seo_keywords: '',
      }
    }

    const submitForm = async () => {
      try {
        if (editingItem.value) {
          await updateArticle(editingItem.value.id, form.value)
        } else {
          await createArticle(form.value)
        }
        closeForm()
        await fetchArticles()
      } catch (error) {
        console.error('提交失败:', error)
      }
    }

    const deleteItem = async (id) => {
      if (confirm('确定要删除这个Article吗？')) {
        try {
          await deleteArticle(id)
          await fetchArticles()
        } catch (error) {
          console.error('删除失败:', error)
        }
      }
    }

    onMounted(() => {
      fetchArticles()
    })

    return {
      items,
      loading,
      showCreateForm,
      editingItem,
      form,
      editItem,
      closeForm,
      submitForm,
      deleteItem
    }
  }
}
</script>

<style scoped>
.article-component {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.item-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field {
  margin-bottom: 8px;
}

.field label {
  font-weight: bold;
  margin-right: 8px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}
</style>
