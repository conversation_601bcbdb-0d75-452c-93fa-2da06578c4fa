"""
基于配置文件的代码生成工具
支持从 YAML 配置文件批量生成模块、模型、API 和前端组件
"""

import os
import json
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.template import Template, Context
from django.conf import settings

try:
    import yaml

    HAS_YAML = True
except ImportError:
    HAS_YAML = False


class Command(BaseCommand):
    help = "基于配置文件的代码生成工具 - 支持批量生成模块、API 和前端组件"

    def add_arguments(self, parser):
        parser.add_argument(
            "--config", type=str, required=True, help="YAML 配置文件路径"
        )
        parser.add_argument(
            "--full",
            action="store_true",
            help="生成完整功能 (模型、API、Admin、权限、测试)",
        )
        parser.add_argument(
            "--with-frontend", action="store_true", help="生成前端 Vue.js 组件"
        )
        parser.add_argument(
            "--output-dir", type=str, default="apps", help="输出目录 (默认: apps)"
        )
        parser.add_argument(
            "--dry-run", action="store_true", help="预览模式，不实际生成文件"
        )

    def handle(self, *args, **options):
        config_file = options["config"]

        # 检查配置文件是否存在
        if not os.path.exists(config_file):
            raise CommandError(f"配置文件不存在: {config_file}")

        # 解析配置文件
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise CommandError(f"配置文件解析错误: {e}")

        self.config = config
        self.options = options

        # 验证配置
        self.validate_config()

        # 开始生成
        self.stdout.write(self.style.SUCCESS("🚀 开始基于配置文件生成代码..."))
        self.stdout.write(f"📁 配置文件: {config_file}")
        self.stdout.write(f'📦 应用名称: {config.get("app_name", "unknown")}')

        if options["dry_run"]:
            self.stdout.write(self.style.WARNING("🔍 预览模式 - 不会实际生成文件"))

        # 生成应用模块
        self.generate_app_module()

        # 生成模型
        self.generate_models()

        if options["full"]:
            # 生成完整功能
            self.generate_admin()
            self.generate_api()
            self.generate_permissions()
            self.generate_tests()

        if options["with_frontend"]:
            # 生成前端组件
            self.generate_frontend()

        # 生成完成提示
        self.show_completion_message()

    def validate_config(self):
        """验证配置文件格式"""
        if "app_name" not in self.config:
            raise CommandError("配置文件必须包含 app_name 字段")

        if "models" not in self.config:
            raise CommandError("配置文件必须包含 models 字段")

        if not isinstance(self.config["models"], dict):
            raise CommandError("models 字段必须是字典格式")

    def generate_app_module(self):
        """生成应用模块结构"""
        app_name = self.config["app_name"]
        output_dir = self.options["output_dir"]

        self.stdout.write(f"\n📦 生成应用模块: {app_name}")

        # 创建目录结构
        app_path = Path(output_dir) / app_name

        if not self.options["dry_run"]:
            app_path.mkdir(exist_ok=True)
            (app_path / "migrations").mkdir(exist_ok=True)

        # 生成基础文件
        files_to_generate = [
            ("__init__.py", ""),
            ("apps.py", self.get_apps_content()),
            ("migrations/__init__.py", ""),
        ]

        for file_name, content in files_to_generate:
            file_path = app_path / file_name
            if not self.options["dry_run"]:
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
            self.stdout.write(f"  ✓ {file_name}")

    def generate_models(self):
        """生成模型文件"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        self.stdout.write(f"\n🗃️  生成模型文件: {len(models)} 个模型")

        # 生成 models.py 内容
        models_content = self.get_models_content()

        if not self.options["dry_run"]:
            models_file = Path(self.options["output_dir"]) / app_name / "models.py"
            with open(models_file, "w", encoding="utf-8") as f:
                f.write(models_content)

        for model_name in models.keys():
            self.stdout.write(f"  ✓ {model_name}")

    def generate_admin(self):
        """生成 Admin 配置"""
        app_name = self.config["app_name"]

        self.stdout.write(f"\n⚙️  生成 Admin 配置")

        admin_content = self.get_admin_content()

        if not self.options["dry_run"]:
            admin_file = Path(self.options["output_dir"]) / app_name / "admin.py"
            with open(admin_file, "w", encoding="utf-8") as f:
                f.write(admin_content)

        self.stdout.write(f"  ✓ admin.py")

    def generate_api(self):
        """生成 API 接口"""
        app_name = self.config["app_name"]

        self.stdout.write(f"\n🔌 生成 API 接口")

        # 生成 schemas.py
        schemas_content = self.get_schemas_content()
        if not self.options["dry_run"]:
            schemas_file = Path(self.options["output_dir"]) / app_name / "schemas.py"
            with open(schemas_file, "w", encoding="utf-8") as f:
                f.write(schemas_content)
        self.stdout.write(f"  ✓ schemas.py")

        # 生成 api.py
        api_content = self.get_api_content()
        if not self.options["dry_run"]:
            api_file = Path(self.options["output_dir"]) / app_name / "api.py"
            with open(api_file, "w", encoding="utf-8") as f:
                f.write(api_content)
        self.stdout.write(f"  ✓ api.py")

        # 生成 services.py
        services_content = self.get_services_content()
        if not self.options["dry_run"]:
            services_file = Path(self.options["output_dir"]) / app_name / "services.py"
            with open(services_file, "w", encoding="utf-8") as f:
                f.write(services_content)
        self.stdout.write(f"  ✓ services.py")

    def generate_permissions(self):
        """生成权限配置"""
        app_name = self.config["app_name"]

        self.stdout.write(f"\n🔐 生成权限配置")

        permissions_content = self.get_permissions_content()

        if not self.options["dry_run"]:
            permissions_file = (
                Path(self.options["output_dir"]) / app_name / "permissions.py"
            )
            with open(permissions_file, "w", encoding="utf-8") as f:
                f.write(permissions_content)

        self.stdout.write(f"  ✓ permissions.py")

    def generate_tests(self):
        """生成测试文件"""
        app_name = self.config["app_name"]

        self.stdout.write(f"\n🧪 生成测试文件")

        tests_content = self.get_tests_content()

        if not self.options["dry_run"]:
            tests_file = Path(self.options["output_dir"]) / app_name / "tests.py"
            with open(tests_file, "w", encoding="utf-8") as f:
                f.write(tests_content)

        self.stdout.write(f"  ✓ tests.py")

    def generate_frontend(self):
        """生成前端组件"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        self.stdout.write(f"\n🎨 生成前端组件: {len(models)} 个组件")

        # 创建前端目录
        frontend_path = Path("frontend") / "src" / "components" / app_name

        if not self.options["dry_run"]:
            frontend_path.mkdir(parents=True, exist_ok=True)

        # 为每个模型生成 Vue 组件
        for model_name, model_config in models.items():
            component_content = self.get_vue_component_content(model_name, model_config)

            if not self.options["dry_run"]:
                component_file = frontend_path / f"{model_name}.vue"
                with open(component_file, "w", encoding="utf-8") as f:
                    f.write(component_content)

            self.stdout.write(f"  ✓ {model_name}.vue")

        # 生成路由配置
        router_content = self.get_router_content()
        if not self.options["dry_run"]:
            router_file = frontend_path / "router.js"
            with open(router_file, "w", encoding="utf-8") as f:
                f.write(router_content)
        self.stdout.write(f"  ✓ router.js")

    def show_completion_message(self):
        """显示完成消息和后续步骤"""
        app_name = self.config["app_name"]

        self.stdout.write(self.style.SUCCESS(f"\n🎉 代码生成完成！"))
        self.stdout.write("\n📋 后续步骤:")
        self.stdout.write(f"  1. 将应用添加到 INSTALLED_APPS: apps.{app_name}")
        self.stdout.write(
            f"  2. 生成迁移文件: python manage.py makemigrations {app_name}"
        )
        self.stdout.write(f"  3. 执行迁移: python manage.py migrate")
        self.stdout.write(
            f"  4. 生成权限: python manage.py auto_generate_permissions {app_name} --create-roles"
        )
        self.stdout.write(f"  5. 在 core/urls.py 中添加路由")

        if self.options["with_frontend"]:
            self.stdout.write(f"  6. 安装前端依赖: npm install")
            self.stdout.write(f"  7. 启动前端开发服务器: npm run dev")

    def get_apps_content(self):
        """生成 apps.py 内容"""
        app_name = self.config["app_name"]
        app_title = app_name.replace("_", " ").title()

        return f'''"""
{app_title} 应用配置
"""
from django.apps import AppConfig


class {app_name.title()}Config(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.{app_name}'
    verbose_name = '{app_title}'

    def ready(self):
        """应用就绪时的初始化操作"""
        try:
            import apps.{app_name}.signals  # noqa
        except ImportError:
            pass
'''

    def get_models_content(self):
        """生成 models.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} 应用数据模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()


'''

        for model_name, model_config in models.items():
            content += self.get_model_class_content(model_name, model_config)
            content += "\n\n"

        return content

    def get_model_class_content(self, model_name, model_config):
        """生成单个模型类内容"""
        fields = model_config.get("fields", {})
        options = model_config.get("options", {})

        content = f'''class {model_name}(BaseModel):
    """{options.get('verbose_name', model_name)} 模型"""

'''

        # 生成字段
        for field_name, field_type in fields.items():
            django_field = self.convert_field_type(field_type, field_name)
            verbose_name = field_name.replace("_", " ").title()
            # 修复字段定义语法
            if not django_field.endswith(")"):
                django_field += "()"
            # 在括号内添加 verbose_name
            if django_field.endswith("()"):
                django_field = django_field[:-1] + f"verbose_name=_('{verbose_name}'))"
            else:
                django_field = (
                    django_field[:-1] + f", verbose_name=_('{verbose_name}'))"
                )
            content += f"    {field_name} = models.{django_field}\n"

        # 添加所有者字段
        content += f"""
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='{model_name.lower()}_set',
        verbose_name=_('所有者')
    )

    class Meta:
        verbose_name = _('{options.get('verbose_name', model_name)}')
        verbose_name_plural = _('{options.get('verbose_name', model_name)} 列表')
        db_table = '{self.config['app_name']}_{model_name.lower()}'
        ordering = {options.get('ordering', ['-created_at'])}
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['created_at']),
        ]

"""

        # 添加 __str__ 方法
        # 智能选择 __str__ 返回字段
        title_field = None
        for field_name in fields.keys():
            if field_name in ['title', 'name', 'label']:
                title_field = field_name
                break

        content += "\n    def __str__(self):"
        if title_field:
            content += f"\n        return self.{title_field}"
        else:
            content += f"\n        return f'{model_name} #{{self.id}}'"

        return content

    def convert_field_type(self, field_type, field_name=""):
        """转换字段类型为 Django 字段"""
        type_mapping = {
            "str": "CharField(max_length=255)",
            "text": "TextField()",
            "int": "IntegerField()",
            "float": "FloatField()",
            "bool": "BooleanField(default=False)",
            "date": "DateField()",
            "datetime": "DateTimeField()",
            "email": "EmailField()",
            "url": "URLField()",
            "json": "JSONField(default=dict)",
            "file": 'FileField(upload_to="uploads/")',
        }

        # 处理外键类型
        if field_type.startswith("fk:"):
            related_model = field_type[3:]
            # 智能处理外键引用
            if related_model == "User":
                related_model = "users.User"
            elif related_model == "self":
                related_model = "self"
            
            # 智能添加约束
            constraints = ""
            if field_name in ["parent", "unsubscribed_at"] or "parent" in field_name.lower():
                constraints = ", null=True, blank=True"
            
            return f'ForeignKey("{related_model}", on_delete=models.CASCADE{constraints})'

        # 处理多对多类型
        if field_type.startswith("m2m:"):
            related_model = field_type[4:]
            return f'ManyToManyField("{related_model}", blank=True)'

        return type_mapping.get(field_type, "CharField(max_length=255)")

    def get_admin_content(self):
        """生成 admin.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} Admin 配置
"""
from django.contrib import admin
from apps.core.admin.auto_admin import register_auto_admin

'''

        # 导入模型
        model_imports = ", ".join(models.keys())
        content += f"from .models import {model_imports}\n\n"

        # 注册模型
        for model_name in models.keys():
            content += f"register_auto_admin({model_name})\n"

        return content

    def get_schemas_content(self):
        """生成 schemas.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} API Schemas
"""
from typing import Optional, List
from datetime import datetime, date
from pydantic import BaseModel, Field

'''

        # 为每个模型生成 Schema
        for model_name, model_config in models.items():
            content += self.get_model_schema_content(model_name, model_config)
            content += "\n\n"

        return content

    def get_model_schema_content(self, model_name, model_config):
        """生成单个模型的 Schema 内容"""
        fields = model_config.get("fields", {})

        # 输入 Schema
        content = f'''class {model_name}CreateSchema(BaseModel):
    """{model_name} 创建 Schema"""
'''

        for field_name, field_type in fields.items():
            pydantic_type = self.convert_to_pydantic_type(field_type)
            content += f"    {field_name}: {pydantic_type}\n"

        # 更新 Schema
        content += f'''

class {model_name}UpdateSchema(BaseModel):
    """{model_name} 更新 Schema"""
'''

        for field_name, field_type in fields.items():
            pydantic_type = self.convert_to_pydantic_type(field_type, optional=True)
            content += f"    {field_name}: {pydantic_type} = None\n"

        # 输出 Schema
        content += f'''

class {model_name}Schema(BaseModel):
    """{model_name} 输出 Schema"""
    id: int
'''

        for field_name, field_type in fields.items():
            pydantic_type = self.convert_to_pydantic_type(field_type)
            content += f"    {field_name}: {pydantic_type}\n"

        content += """    owner_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
"""

        return content

    def convert_to_pydantic_type(self, field_type, optional=False, field_name=""):
        """转换字段类型为 Pydantic 类型"""
        type_mapping = {
            "str": "str",
            "text": "str",
            "int": "int",
            "float": "float",
            "bool": "bool",
            "date": "date",
            "datetime": "datetime",
            "email": "str",
            "url": "str",
            "json": "dict",
            "file": "str",
        }

        # 处理外键类型
        if field_type.startswith("fk:"):
            pydantic_type = "int"
        # 处理多对多类型
        elif field_type.startswith("m2m:"):
            pydantic_type = "List[int]"
        else:
            pydantic_type = type_mapping.get(field_type, "str")

        if optional:
            return f"Optional[{pydantic_type}]"
        return pydantic_type

    def get_api_content(self):
        """生成 api.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} API 接口
"""
from typing import List
from ninja import Router
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from apps.permissions.decorators import require_permissions

'''

        # 导入模型和 Schema
        model_imports = ", ".join(models.keys())
        content += f"from .models import {model_imports}\n"

        schema_imports = []
        for model_name in models.keys():
            schema_imports.extend(
                [
                    f"{model_name}Schema",
                    f"{model_name}CreateSchema",
                    f"{model_name}UpdateSchema",
                ]
            )
        content += f'from .schemas import {", ".join(schema_imports)}\n'
        content += f'from .services import {", ".join([f"{model}Service" for model in models.keys()])}\n\n'

        content += f'router = Router(tags=["{app_name.title()}"])\n\n'
        content += f'# 导出路由器\n{app_name}_router = router\n\n'

        # 为每个模型生成 API 端点
        for model_name in models.keys():
            content += self.get_model_api_content(model_name)
            content += "\n\n"

        return content

    def get_model_api_content(self, model_name):
        """生成单个模型的 API 内容"""
        model_lower = model_name.lower()
        service_name = f"{model_name}Service"

        content = f'''# {model_name} API 端点
@router.get("/{model_lower}/", response=List[{model_name}Schema])
@login_required
@require_permissions('{self.config['app_name']}.{model_lower}.view')
def list_{model_lower}(request):
    """{model_name} 列表"""
    return {service_name}.get_user_items(request.user)


@router.get("/{model_lower}/{{item_id}}/", response={model_name}Schema)
@login_required
@require_permissions('{self.config['app_name']}.{model_lower}.view')
def get_{model_lower}(request, item_id: int):
    """{model_name} 详情"""
    return {service_name}.get_user_item(request.user, item_id)


@router.post("/{model_lower}/", response={model_name}Schema)
@login_required
@require_permissions('{self.config['app_name']}.{model_lower}.add')
def create_{model_lower}(request, data: {model_name}CreateSchema):
    """创建 {model_name}"""
    return {service_name}.create_item(request.user, data.dict())


@router.put("/{model_lower}/{{item_id}}/", response={model_name}Schema)
@login_required
@require_permissions('{self.config['app_name']}.{model_lower}.change')
def update_{model_lower}(request, item_id: int, data: {model_name}UpdateSchema):
    """更新 {model_name}"""
    return {service_name}.update_item(request.user, item_id, data.dict(exclude_unset=True))


@router.delete("/{model_lower}/{{item_id}}/")
@login_required
@require_permissions('{self.config['app_name']}.{model_lower}.delete')
def delete_{model_lower}(request, item_id: int):
    """删除 {model_name}"""
    {service_name}.delete_item(request.user, item_id)
    return {{"message": "{model_name} 删除成功"}}'''

        return content

    def get_services_content(self):
        """生成 services.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} 业务逻辑服务
"""
from typing import List, Dict, Any
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.core.exceptions import PermissionDenied

'''

        # 导入模型
        model_imports = ", ".join(models.keys())
        content += f"from .models import {model_imports}\n\n"
        content += "User = get_user_model()\n\n"

        # 为每个模型生成服务类
        for model_name in models.keys():
            content += self.get_model_service_content(model_name)
            content += "\n\n"

        return content

    def get_model_service_content(self, model_name):
        """生成单个模型的服务内容"""
        content = f'''class {model_name}Service:
    """{model_name} 业务逻辑服务"""

    @staticmethod
    def get_user_items(user: User) -> List[{model_name}]:
        """获取用户的 {model_name} 列表"""
        return {model_name}.objects.filter(owner=user).order_by('-created_at')

    @staticmethod
    def get_user_item(user: User, item_id: int) -> {model_name}:
        """获取用户的单个 {model_name}"""
        return get_object_or_404({model_name}, id=item_id, owner=user)

    @staticmethod
    def create_item(user: User, data: Dict[str, Any]) -> {model_name}:
        """创建 {model_name}"""
        data['owner'] = user
        return {model_name}.objects.create(**data)

    @staticmethod
    def update_item(user: User, item_id: int, data: Dict[str, Any]) -> {model_name}:
        """更新 {model_name}"""
        item = {model_name}Service.get_user_item(user, item_id)
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        item.save()
        return item

    @staticmethod
    def delete_item(user: User, item_id: int) -> None:
        """删除 {model_name}"""
        item = {model_name}Service.get_user_item(user, item_id)
        item.delete()'''

        return content

    def get_permissions_content(self):
        """生成 permissions.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} 权限配置
"""
from apps.permissions.models import Permission, Role

# 权限定义
PERMISSIONS = [
'''

        # 为每个模型生成权限
        for model_name in models.keys():
            model_lower = model_name.lower()
            content += f"""    # {model_name} 权限
    {{
        'codename': '{app_name}.{model_lower}.view',
        'name': '查看 {model_name}',
        'content_type': '{app_name}.{model_lower}',
    }},
    {{
        'codename': '{app_name}.{model_lower}.add',
        'name': '添加 {model_name}',
        'content_type': '{app_name}.{model_lower}',
    }},
    {{
        'codename': '{app_name}.{model_lower}.change',
        'name': '修改 {model_name}',
        'content_type': '{app_name}.{model_lower}',
    }},
    {{
        'codename': '{app_name}.{model_lower}.delete',
        'name': '删除 {model_name}',
        'content_type': '{app_name}.{model_lower}',
    }},
"""

        content += ''']

# 角色定义
ROLES = [
    {
        'name': f'{app_name.title()} 管理员',
        'codename': f'{app_name}_admin',
        'permissions': [p['codename'] for p in PERMISSIONS],
    },
    {
        'name': f'{app_name.title()} 编辑者',
        'codename': f'{app_name}_editor',
        'permissions': [p['codename'] for p in PERMISSIONS if not p['codename'].endswith('.delete')],
    },
    {
        'name': f'{app_name.title()} 查看者',
        'codename': f'{app_name}_viewer',
        'permissions': [p['codename'] for p in PERMISSIONS if p['codename'].endswith('.view')],
    },
]


def setup_permissions():
    """设置权限和角色"""
    # 创建权限
    for perm_data in PERMISSIONS:
        Permission.objects.get_or_create(
            codename=perm_data['codename'],
            defaults=perm_data
        )

    # 创建角色
    for role_data in ROLES:
        role, created = Role.objects.get_or_create(
            codename=role_data['codename'],
            defaults={'name': role_data['name']}
        )

        if created:
            # 分配权限
            permissions = Permission.objects.filter(
                codename__in=role_data['permissions']
            )
            role.permissions.set(permissions)
'''

        return content

    def get_tests_content(self):
        """生成 tests.py 内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f'''"""
{app_name.title()} 测试用例
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status

'''

        # 导入模型
        model_imports = ", ".join(models.keys())
        content += f"from .models import {model_imports}\n"
        content += f'from .services import {", ".join([f"{model}Service" for model in models.keys()])}\n\n'
        content += "User = get_user_model()\n\n"

        # 为每个模型生成测试类
        for model_name in models.keys():
            content += self.get_model_test_content(model_name)
            content += "\n\n"

        return content

    def get_model_test_content(self, model_name):
        """生成单个模型的测试内容"""
        content = f'''class {model_name}TestCase(TestCase):
    """{model_name} 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_{model_name.lower()}(self):
        """测试创建 {model_name}"""
        data = {{
            'name': 'Test {model_name}',
            'description': 'Test description',
        }}
        item = {model_name}Service.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test {model_name}')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        {model_name}Service.create_item(self.user, {{'name': 'Item 1'}})
        {model_name}Service.create_item(self.user, {{'name': 'Item 2'}})

        items = {model_name}Service.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class {model_name}APITestCase(APITestCase):
    """{model_name} API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_{model_name.lower()}(self):
        """测试 {model_name} 列表 API"""
        url = reverse('{self.config['app_name']}:{model_name.lower()}-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_{model_name.lower()}(self):
        """测试创建 {model_name} API"""
        url = reverse('{self.config['app_name']}:{model_name.lower()}-list')
        data = {{
            'name': 'Test {model_name}',
            'description': 'Test description',
        }}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)'''

        return content

    def get_vue_component_content(self, model_name, model_config):
        """生成 Vue 组件内容"""
        fields = model_config.get("fields", {})

        content = f"""<template>
  <div class="{model_name.lower()}-component">
    <div class="header">
      <h2>{model_name} 管理</h2>
      <button @click="showCreateForm = true" class="btn btn-primary">
        添加 {model_name}
      </button>
    </div>

    <!-- 列表 -->
    <div class="list-container">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else>
        <div v-for="item in items" :key="item.id" class="item-card">
          <div class="item-content">
"""

        # 生成字段显示
        for field_name in list(fields.keys())[:3]:  # 只显示前3个字段
            content += f'            <div class="field">\n'
            content += f'              <label>{field_name.replace("_", " ").title()}:</label>\n'
            content += f"              <span>{{{{ item.{field_name} }}}}</span>\n"
            content += f"            </div>\n"

        content += f"""          </div>
          <div class="item-actions">
            <button @click="editItem(item)" class="btn btn-sm btn-secondary">编辑</button>
            <button @click="deleteItem(item.id)" class="btn btn-sm btn-danger">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑表单 -->
    <div v-if="showCreateForm || editingItem" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{{{ editingItem ? '编辑' : '创建' }}}} {model_name}</h3>
          <button @click="closeForm" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="submitForm" class="form">
"""

        # 生成表单字段
        for field_name, field_type in fields.items():
            input_type = self.get_input_type(field_type)
            content += f"""          <div class="form-group">
            <label for="{field_name}">{field_name.replace("_", " ").title()}:</label>
            <{input_type}
              id="{field_name}"
              v-model="form.{field_name}"
              required
            />
          </div>
"""

        content += f"""          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              {{{{ editingItem ? '更新' : '创建' }}}}
            </button>
            <button type="button" @click="closeForm" class="btn btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import {{ ref, onMounted }} from 'vue'
import {{ use{model_name}API }} from '../composables/use{model_name}API'

export default {{
  name: '{model_name}Component',
  setup() {{
    const {{ items, loading, create{model_name}, update{model_name}, delete{model_name}, fetch{model_name}s }} = use{model_name}API()

    const showCreateForm = ref(false)
    const editingItem = ref(null)
    const form = ref({{
"""

        # 生成表单初始值
        for field_name, field_type in fields.items():
            default_value = self.get_default_value(field_type)
            content += f"      {field_name}: {default_value},\n"

        content += f"""    }})

    const editItem = (item) => {{
      editingItem.value = item
      form.value = {{ ...item }}
    }}

    const closeForm = () => {{
      showCreateForm.value = false
      editingItem.value = null
      resetForm()
    }}

    const resetForm = () => {{
      form.value = {{
"""

        # 重置表单
        for field_name, field_type in fields.items():
            default_value = self.get_default_value(field_type)
            content += f"        {field_name}: {default_value},\n"

        content += f"""      }}
    }}

    const submitForm = async () => {{
      try {{
        if (editingItem.value) {{
          await update{model_name}(editingItem.value.id, form.value)
        }} else {{
          await create{model_name}(form.value)
        }}
        closeForm()
        await fetch{model_name}s()
      }} catch (error) {{
        console.error('提交失败:', error)
      }}
    }}

    const deleteItem = async (id) => {{
      if (confirm('确定要删除这个{model_name}吗？')) {{
        try {{
          await delete{model_name}(id)
          await fetch{model_name}s()
        }} catch (error) {{
          console.error('删除失败:', error)
        }}
      }}
    }}

    onMounted(() => {{
      fetch{model_name}s()
    }})

    return {{
      items,
      loading,
      showCreateForm,
      editingItem,
      form,
      editItem,
      closeForm,
      submitForm,
      deleteItem
    }}
  }}
}}
</script>

<style scoped>
.{model_name.lower()}-component {{
  padding: 20px;
}}

.header {{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}}

.item-card {{
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}}

.field {{
  margin-bottom: 8px;
}}

.field label {{
  font-weight: bold;
  margin-right: 8px;
}}

.modal {{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}}

.modal-content {{
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
}}

.form-group {{
  margin-bottom: 16px;
}}

.form-group label {{
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}}

.form-group input,
.form-group textarea,
.form-group select {{
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}}

.btn {{
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}}

.btn-primary {{
  background: #007bff;
  color: white;
}}

.btn-secondary {{
  background: #6c757d;
  color: white;
}}

.btn-danger {{
  background: #dc3545;
  color: white;
}}
</style>
"""

        return content

    def get_input_type(self, field_type):
        """获取输入框类型"""
        type_mapping = {
            "str": 'input type="text"',
            "text": "textarea",
            "int": 'input type="number"',
            "float": 'input type="number" step="0.01"',
            "bool": 'input type="checkbox"',
            "date": 'input type="date"',
            "datetime": 'input type="datetime-local"',
            "email": 'input type="email"',
            "url": 'input type="url"',
            "file": 'input type="file"',
        }

        if field_type.startswith("fk:") or field_type.startswith("m2m:"):
            return "select"

        return type_mapping.get(field_type, 'input type="text"')

    def get_default_value(self, field_type):
        """获取字段默认值"""
        if field_type == "bool":
            return "false"
        elif field_type in ["int", "float"]:
            return "0"
        elif field_type.startswith("m2m:"):
            return "[]"
        else:
            return "''"

    def get_router_content(self):
        """生成路由配置内容"""
        app_name = self.config["app_name"]
        models = self.config["models"]

        content = f"""/**
 * {app_name.title()} 路由配置
 */
import {{ createRouter, createWebHistory }} from 'vue-router'

"""

        # 导入组件
        for model_name in models.keys():
            content += f"import {model_name}Component from './{model_name}.vue'\n"

        content += f"""
const routes = [
"""

        # 生成路由
        for model_name in models.keys():
            model_lower = model_name.lower()
            content += f"""  {{
    path: '/{model_lower}',
    name: '{model_name}',
    component: {model_name}Component
  }},
"""

        content += f"""]

const router = createRouter({{
  history: createWebHistory(),
  routes
}})

export default router
"""

        return content
