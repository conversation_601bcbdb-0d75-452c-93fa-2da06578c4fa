# 增强版模型配置示例
# 支持更多字段参数和配置选项

app_name: enhanced_blog
models:
  Article:
    fields:
      # 基本字符串字段
      title:
        type: str
        max_length: 200
        null: false
        blank: false
        help_text: "文章标题，最多200个字符"
        verbose_name: "标题"
      
      # 唯一字段
      slug:
        type: str
        max_length: 100
        unique: true
        null: false
        blank: false
        help_text: "URL友好的标识符"
        verbose_name: "URL标识"
      
      # 长文本字段
      content:
        type: text
        null: false
        blank: false
        help_text: "文章正文内容"
        verbose_name: "内容"
      
      # 可选文本字段
      summary:
        type: str
        max_length: 500
        null: true
        blank: true
        help_text: "文章摘要，可选"
        verbose_name: "摘要"
      
      # 选择字段
      status:
        type: choices
        choices:
          - ["draft", "草稿"]
          - ["published", "已发布"]
          - ["archived", "已归档"]
          - ["deleted", "已删除"]
        default: "draft"
        help_text: "文章发布状态"
        verbose_name: "状态"
      
      # 布尔字段
      is_featured:
        type: bool
        default: false
        help_text: "是否为推荐文章"
        verbose_name: "推荐"
      
      is_top:
        type: bool
        default: false
        help_text: "是否置顶显示"
        verbose_name: "置顶"
      
      # 整数字段
      view_count:
        type: int
        default: 0
        help_text: "浏览次数"
        verbose_name: "浏览数"
        validators:
          - min_value: 0
      
      like_count:
        type: int
        default: 0
        help_text: "点赞次数"
        verbose_name: "点赞数"
        validators:
          - min_value: 0
      
      # 浮点数字段
      rating:
        type: float
        null: true
        blank: true
        help_text: "文章评分 (0.0-5.0)"
        verbose_name: "评分"
        validators:
          - min_value: 0.0
          - max_value: 5.0
      
      # 日期时间字段
      published_at:
        type: datetime
        null: true
        blank: true
        help_text: "发布时间"
        verbose_name: "发布时间"
      
      # 邮箱字段
      author_email:
        type: email
        null: true
        blank: true
        help_text: "作者邮箱"
        verbose_name: "作者邮箱"
      
      # URL字段
      external_url:
        type: url
        null: true
        blank: true
        help_text: "外部链接"
        verbose_name: "外部链接"
      
      # JSON字段
      metadata:
        type: json
        default: {}
        help_text: "额外的元数据信息"
        verbose_name: "元数据"
      
      # 文件字段
      attachment:
        type: file
        upload_to: "articles/attachments/"
        null: true
        blank: true
        help_text: "附件文件"
        verbose_name: "附件"
      
      # 外键字段
      author:
        type: fk
        to: User
        on_delete: CASCADE
        null: false
        help_text: "文章作者"
        verbose_name: "作者"
        related_name: "authored_articles"
      
      category:
        type: fk
        to: Category
        on_delete: SET_NULL
        null: true
        blank: true
        help_text: "文章分类"
        verbose_name: "分类"
      
      # 多对多字段
      tags:
        type: m2m
        to: Tag
        blank: true
        help_text: "文章标签"
        verbose_name: "标签"
        related_name: "tagged_articles"
    
    options:
      ordering: ['-published_at', '-created_at']
      verbose_name: '文章'
      verbose_name_plural: '文章'
      db_table: 'enhanced_blog_article'
      indexes:
        - fields: ['status', 'published_at']
        - fields: ['author', 'status']
        - fields: ['category']
      constraints:
        - unique_together: [['slug', 'author']]
      permissions:
        - ['view_article', '查看文章']
        - ['add_article', '添加文章']
        - ['change_article', '修改文章']
        - ['delete_article', '删除文章']
        - ['publish_article', '发布文章']

  Category:
    fields:
      name:
        type: str
        max_length: 100
        unique: true
        help_text: "分类名称"
        verbose_name: "名称"
      
      slug:
        type: str
        max_length: 100
        unique: true
        help_text: "URL标识符"
        verbose_name: "标识符"
      
      description:
        type: text
        null: true
        blank: true
        help_text: "分类描述"
        verbose_name: "描述"
      
      parent:
        type: fk
        to: self
        on_delete: CASCADE
        null: true
        blank: true
        help_text: "父级分类"
        verbose_name: "父分类"
        related_name: "children"
      
      is_active:
        type: bool
        default: true
        help_text: "是否启用"
        verbose_name: "启用"
      
      sort_order:
        type: int
        default: 0
        help_text: "排序顺序"
        verbose_name: "排序"
    
    options:
      ordering: ['sort_order', 'name']
      verbose_name: '分类'
      verbose_name_plural: '分类'

  Tag:
    fields:
      name:
        type: str
        max_length: 50
        unique: true
        help_text: "标签名称"
        verbose_name: "名称"
      
      color:
        type: str
        max_length: 7
        default: "#007bff"
        help_text: "标签颜色 (HEX格式)"
        verbose_name: "颜色"
        validators:
          - regex: "^#[0-9A-Fa-f]{6}$"
      
      is_active:
        type: bool
        default: true
        help_text: "是否启用"
        verbose_name: "启用"
    
    options:
      ordering: ['name']
      verbose_name: '标签'
      verbose_name_plural: '标签'
