"""
URL configuration for django_ninja_template project.
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from ninja import <PERSON><PERSON><PERSON>
from ninja_jwt.authentication import <PERSON><PERSON><PERSON><PERSON>

from apps.authentication.api import auth_router
from apps.users.api import users_router
from apps.permissions.api import permissions_router
from apps.storage.api import storage_router
from apps.i18n.api import i18n_router
from apps.core.api import core_router
from apps.cms.api import cms_router


# 创建主 API 实例
api = NinjaAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
    docs_url="/docs/",
    openapi_url="/api/openapi.json",
)

# 将路由添加到主 API
api.add_router("/auth/", auth_router, tags=["Authentication"])
api.add_router("/users/", users_router, tags=["Users"])
api.add_router("/permissions/", permissions_router, tags=["Permissions"])
api.add_router("/storage/", storage_router, tags=["File Storage"])
api.add_router("/i18n/", i18n_router, tags=["Internationalization"])
api.add_router("/core/", core_router, tags=["Core"])

api.add_router("/cms/", cms_router, tags=["Content Management"])

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", api.urls),
    path("health/", include("apps.core.urls")),
]

# 在开发环境中提供静态文件和媒体文件
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # Add debug toolbar URLs
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar

        urlpatterns = [
            path("__debug__/", include(debug_toolbar.urls)),
        ] + urlpatterns
