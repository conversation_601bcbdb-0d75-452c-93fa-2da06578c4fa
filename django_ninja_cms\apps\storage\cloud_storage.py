"""
云存储服务集成
支持AWS S3、阿里云OSS、腾讯云COS等主流云存储服务
"""
import os
import uuid
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile


class CloudStorageBackend(ABC):
    """云存储后端抽象基类"""
    
    @abstractmethod
    def upload_file(self, file: UploadedFile, key: str) -> Dict[str, Any]:
        """上传文件到云存储"""
        pass
    
    @abstractmethod
    def get_download_url(self, key: str, expires_in: int = 3600) -> str:
        """获取文件下载URL"""
        pass
    
    @abstractmethod
    def delete_file(self, key: str) -> bool:
        """删除云存储文件"""
        pass
    
    @abstractmethod
    def get_file_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        pass


class AWSS3Backend(CloudStorageBackend):
    """AWS S3存储后端"""
    
    def __init__(self):
        self.bucket_name = getattr(settings, 'AWS_STORAGE_BUCKET_NAME', '')
        self.region = getattr(settings, 'AWS_S3_REGION_NAME', 'us-east-1')
        self.access_key = getattr(settings, 'AWS_ACCESS_KEY_ID', '')
        self.secret_key = getattr(settings, 'AWS_SECRET_ACCESS_KEY', '')
        
        if not all([self.bucket_name, self.access_key, self.secret_key]):
            raise ValueError("AWS S3 credentials not properly configured")
    
    def _get_client(self):
        """获取S3客户端"""
        try:
            import boto3
            return boto3.client(
                's3',
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                region_name=self.region
            )
        except ImportError:
            raise ImportError("boto3 is required for AWS S3 backend. Install with: pip install boto3")
    
    def upload_file(self, file: UploadedFile, key: str) -> Dict[str, Any]:
        """上传文件到S3"""
        client = self._get_client()
        
        try:
            # 上传文件
            client.upload_fileobj(
                file,
                self.bucket_name,
                key,
                ExtraArgs={
                    'ContentType': file.content_type,
                    'Metadata': {
                        'original_name': file.name,
                        'upload_id': str(uuid.uuid4())
                    }
                }
            )
            
            # 获取文件信息
            response = client.head_object(Bucket=self.bucket_name, Key=key)
            
            return {
                'key': key,
                'bucket': self.bucket_name,
                'size': response['ContentLength'],
                'etag': response['ETag'].strip('"'),
                'last_modified': response['LastModified'],
                'content_type': response['ContentType'],
                'url': f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{key}"
            }
        except Exception as e:
            raise Exception(f"Failed to upload file to S3: {str(e)}")
    
    def get_download_url(self, key: str, expires_in: int = 3600) -> str:
        """生成S3预签名下载URL"""
        client = self._get_client()
        
        try:
            url = client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': key},
                ExpiresIn=expires_in
            )
            return url
        except Exception as e:
            raise Exception(f"Failed to generate S3 download URL: {str(e)}")
    
    def delete_file(self, key: str) -> bool:
        """删除S3文件"""
        client = self._get_client()
        
        try:
            client.delete_object(Bucket=self.bucket_name, Key=key)
            return True
        except Exception:
            return False
    
    def get_file_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取S3文件信息"""
        client = self._get_client()
        
        try:
            response = client.head_object(Bucket=self.bucket_name, Key=key)
            return {
                'key': key,
                'size': response['ContentLength'],
                'etag': response['ETag'].strip('"'),
                'last_modified': response['LastModified'],
                'content_type': response['ContentType']
            }
        except Exception:
            return None


class AliyunOSSBackend(CloudStorageBackend):
    """阿里云OSS存储后端"""
    
    def __init__(self):
        self.bucket_name = getattr(settings, 'ALIYUN_OSS_BUCKET_NAME', '')
        self.endpoint = getattr(settings, 'ALIYUN_OSS_ENDPOINT', '')
        self.access_key = getattr(settings, 'ALIYUN_OSS_ACCESS_KEY_ID', '')
        self.secret_key = getattr(settings, 'ALIYUN_OSS_ACCESS_KEY_SECRET', '')
        
        if not all([self.bucket_name, self.endpoint, self.access_key, self.secret_key]):
            raise ValueError("Aliyun OSS credentials not properly configured")
    
    def _get_bucket(self):
        """获取OSS bucket对象"""
        try:
            import oss2
            auth = oss2.Auth(self.access_key, self.secret_key)
            return oss2.Bucket(auth, self.endpoint, self.bucket_name)
        except ImportError:
            raise ImportError("oss2 is required for Aliyun OSS backend. Install with: pip install oss2")
    
    def upload_file(self, file: UploadedFile, key: str) -> Dict[str, Any]:
        """上传文件到OSS"""
        bucket = self._get_bucket()
        
        try:
            # 上传文件
            result = bucket.put_object(
                key,
                file,
                headers={
                    'Content-Type': file.content_type,
                    'x-oss-meta-original-name': file.name,
                    'x-oss-meta-upload-id': str(uuid.uuid4())
                }
            )
            
            return {
                'key': key,
                'bucket': self.bucket_name,
                'etag': result.etag,
                'request_id': result.request_id,
                'url': f"https://{self.bucket_name}.{self.endpoint.replace('https://', '')}/{key}"
            }
        except Exception as e:
            raise Exception(f"Failed to upload file to OSS: {str(e)}")
    
    def get_download_url(self, key: str, expires_in: int = 3600) -> str:
        """生成OSS预签名下载URL"""
        bucket = self._get_bucket()
        
        try:
            url = bucket.sign_url('GET', key, expires_in)
            return url
        except Exception as e:
            raise Exception(f"Failed to generate OSS download URL: {str(e)}")
    
    def delete_file(self, key: str) -> bool:
        """删除OSS文件"""
        bucket = self._get_bucket()
        
        try:
            bucket.delete_object(key)
            return True
        except Exception:
            return False
    
    def get_file_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取OSS文件信息"""
        bucket = self._get_bucket()
        
        try:
            info = bucket.get_object_meta(key)
            return {
                'key': key,
                'size': int(info.headers.get('Content-Length', 0)),
                'etag': info.headers.get('ETag', '').strip('"'),
                'last_modified': info.headers.get('Last-Modified'),
                'content_type': info.headers.get('Content-Type')
            }
        except Exception:
            return None


class TencentCOSBackend(CloudStorageBackend):
    """腾讯云COS存储后端"""
    
    def __init__(self):
        self.bucket_name = getattr(settings, 'TENCENT_COS_BUCKET_NAME', '')
        self.region = getattr(settings, 'TENCENT_COS_REGION', '')
        self.secret_id = getattr(settings, 'TENCENT_COS_SECRET_ID', '')
        self.secret_key = getattr(settings, 'TENCENT_COS_SECRET_KEY', '')
        
        if not all([self.bucket_name, self.region, self.secret_id, self.secret_key]):
            raise ValueError("Tencent COS credentials not properly configured")
    
    def _get_client(self):
        """获取COS客户端"""
        try:
            from qcloud_cos import CosConfig, CosS3Client
            config = CosConfig(
                Region=self.region,
                SecretId=self.secret_id,
                SecretKey=self.secret_key
            )
            return CosS3Client(config)
        except ImportError:
            raise ImportError("cos-python-sdk-v5 is required for Tencent COS backend. Install with: pip install cos-python-sdk-v5")
    
    def upload_file(self, file: UploadedFile, key: str) -> Dict[str, Any]:
        """上传文件到COS"""
        client = self._get_client()
        
        try:
            response = client.upload_file_from_buffer(
                Bucket=self.bucket_name,
                Key=key,
                Body=file.read(),
                ContentType=file.content_type
            )
            
            return {
                'key': key,
                'bucket': self.bucket_name,
                'etag': response['ETag'].strip('"'),
                'url': f"https://{self.bucket_name}.cos.{self.region}.myqcloud.com/{key}"
            }
        except Exception as e:
            raise Exception(f"Failed to upload file to COS: {str(e)}")
    
    def get_download_url(self, key: str, expires_in: int = 3600) -> str:
        """生成COS预签名下载URL"""
        client = self._get_client()
        
        try:
            url = client.get_presigned_download_url(
                Bucket=self.bucket_name,
                Key=key,
                Expired=expires_in
            )
            return url
        except Exception as e:
            raise Exception(f"Failed to generate COS download URL: {str(e)}")
    
    def delete_file(self, key: str) -> bool:
        """删除COS文件"""
        client = self._get_client()
        
        try:
            client.delete_object(Bucket=self.bucket_name, Key=key)
            return True
        except Exception:
            return False
    
    def get_file_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取COS文件信息"""
        client = self._get_client()
        
        try:
            response = client.head_object(Bucket=self.bucket_name, Key=key)
            return {
                'key': key,
                'size': int(response.get('Content-Length', 0)),
                'etag': response.get('ETag', '').strip('"'),
                'last_modified': response.get('Last-Modified'),
                'content_type': response.get('Content-Type')
            }
        except Exception:
            return None


class CloudStorageManager:
    """云存储管理器"""
    
    BACKENDS = {
        'aws_s3': AWSS3Backend,
        'aliyun_oss': AliyunOSSBackend,
        'tencent_cos': TencentCOSBackend,
    }
    
    def __init__(self, backend_name: str = None):
        backend_name = backend_name or getattr(settings, 'DEFAULT_CLOUD_STORAGE_BACKEND', 'aws_s3')
        
        if backend_name not in self.BACKENDS:
            raise ValueError(f"Unsupported cloud storage backend: {backend_name}")
        
        self.backend = self.BACKENDS[backend_name]()
        self.backend_name = backend_name
    
    def generate_key(self, filename: str, user_id: int = None) -> str:
        """生成云存储文件key"""
        from datetime import datetime
        
        # 获取文件扩展名
        ext = os.path.splitext(filename)[1]
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}{ext}"
        
        # 按日期和用户组织路径
        now = datetime.now()
        if user_id:
            key = f"uploads/{user_id}/{now.year}/{now.month:02d}/{now.day:02d}/{unique_filename}"
        else:
            key = f"uploads/{now.year}/{now.month:02d}/{now.day:02d}/{unique_filename}"
        
        return key
    
    def upload_file(self, file: UploadedFile, user_id: int = None) -> Dict[str, Any]:
        """上传文件到云存储"""
        key = self.generate_key(file.name, user_id)
        result = self.backend.upload_file(file, key)
        result['backend'] = self.backend_name
        return result
    
    def get_download_url(self, key: str, expires_in: int = 3600) -> str:
        """获取下载URL"""
        return self.backend.get_download_url(key, expires_in)
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        return self.backend.delete_file(key)
    
    def get_file_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        return self.backend.get_file_info(key)