"""
File storage models - 按照教程03设计实现
"""
import os
import uuid
import hashlib
from django.db import models
from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from PIL import Image

User = get_user_model()


def upload_to(instance, filename):
    """生成文件上传路径"""
    # 按日期和用户ID组织文件
    ext = filename.split('.')[-1] if '.' in filename else 'txt'
    filename = f"{uuid.uuid4().hex}.{ext}"
    
    # 使用当前日期而不是instance.created_at（因为此时可能还未保存）
    from datetime import datetime
    now = datetime.now()
    return f"uploads/{instance.uploaded_by.id}/{now.strftime('%Y/%m/%d')}/{filename}"


class FileCategory(models.Model):
    """文件分类"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    allowed_extensions = models.JSONField(default=list)  # 允许的文件扩展名
    max_file_size = models.BigIntegerField(default=10*1024*1024)  # 最大文件大小(字节)

    class Meta:
        verbose_name = "文件分类"
        verbose_name_plural = "文件分类"

    def __str__(self):
        return self.name


class StorageFile(models.Model):
    """文件存储模型"""

    # 文件状态
    STATUS_UPLOADING = 'uploading'
    STATUS_COMPLETED = 'completed'
    STATUS_FAILED = 'failed'
    STATUS_DELETED = 'deleted'

    STATUS_CHOICES = [
        (STATUS_UPLOADING, '上传中'),
        (STATUS_COMPLETED, '已完成'),
        (STATUS_FAILED, '上传失败'),
        (STATUS_DELETED, '已删除'),
    ]
    
    # 存储后端选择
    STORAGE_LOCAL = 'local'
    STORAGE_AWS_S3 = 'aws_s3'
    STORAGE_ALIYUN_OSS = 'aliyun_oss'
    STORAGE_TENCENT_COS = 'tencent_cos'
    
    STORAGE_BACKEND_CHOICES = [
        (STORAGE_LOCAL, '本地存储'),
        (STORAGE_AWS_S3, 'AWS S3'),
        (STORAGE_ALIYUN_OSS, '阿里云OSS'),
        (STORAGE_TENCENT_COS, '腾讯云COS'),
    ]

    # 基本信息
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    original_name = models.CharField(max_length=255)  # 原始文件名
    file = models.FileField(upload_to=upload_to, blank=True, null=True)  # 本地文件（可选）
    file_size = models.BigIntegerField()  # 文件大小(字节)
    content_type = models.CharField(max_length=100)  # MIME类型
    file_hash = models.CharField(max_length=64)  # 文件哈希值（允许重复）

    # 存储信息
    storage_backend = models.CharField(
        max_length=20, 
        choices=STORAGE_BACKEND_CHOICES, 
        default=STORAGE_LOCAL
    )  # 存储后端
    storage_key = models.CharField(max_length=500, blank=True)  # 云存储文件key
    storage_url = models.URLField(blank=True)  # 云存储文件URL

    # 分类和权限
    category = models.ForeignKey(FileCategory, on_delete=models.SET_NULL, null=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='uploaded_files')
    is_public = models.BooleanField(default=False)  # 是否公开访问

    # 状态和统计
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_UPLOADING)
    download_count = models.PositiveIntegerField(default=0)  # 下载次数

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # 元数据
    metadata = models.JSONField(default=dict)  # 额外的文件元数据

    class Meta:
        verbose_name = "存储文件"
        verbose_name_plural = "存储文件"
        ordering = ['-created_at']

    def __str__(self):
        return self.original_name

    @property
    def file_extension(self):
        """获取文件扩展名"""
        return os.path.splitext(self.original_name)[1].lower()

    @property
    def is_image(self):
        """判断是否为图片文件"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        return self.file_extension in image_extensions

    @property
    def human_readable_size(self):
        """人类可读的文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    @property
    def is_cloud_storage(self):
        """判断是否为云存储"""
        return self.storage_backend != self.STORAGE_LOCAL
    
    def get_download_url(self, expires_in: int = 3600):
        """获取下载URL"""
        if self.storage_backend == self.STORAGE_LOCAL:
            # 本地存储直接返回文件URL
            return self.file.url if self.file else None
        else:
            # 云存储生成预签名URL
            from .cloud_storage import CloudStorageManager
            try:
                manager = CloudStorageManager(self.storage_backend)
                return manager.get_download_url(self.storage_key, expires_in)
            except Exception:
                return self.storage_url  # 降级到静态URL

    def calculate_hash(self):
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        if self.file:
            for chunk in iter(lambda: self.file.read(4096), b""):
                hash_md5.update(chunk)
            self.file.seek(0)  # 重置文件指针
        return hash_md5.hexdigest()

    def save(self, *args, **kwargs):
        if not self.file_hash and self.file:
            self.file_hash = self.calculate_hash()
        super().save(*args, **kwargs)


class ImageThumbnail(models.Model):
    """图片缩略图"""
    original_file = models.ForeignKey(
        StorageFile,
        on_delete=models.CASCADE,
        related_name='thumbnails'
    )
    size = models.CharField(max_length=20)  # 如: "150x150", "300x200"
    thumbnail = models.ImageField(upload_to='thumbnails/')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['original_file', 'size']
        verbose_name = "缩略图"
        verbose_name_plural = "缩略图"


class FileShare(models.Model):
    """文件分享"""
    file = models.ForeignKey(StorageFile, on_delete=models.CASCADE, related_name='shares')
    share_token = models.UUIDField(default=uuid.uuid4, unique=True)
    shared_by = models.ForeignKey(User, on_delete=models.CASCADE)
    expires_at = models.DateTimeField(null=True, blank=True)  # 过期时间
    download_limit = models.PositiveIntegerField(null=True, blank=True)  # 下载次数限制
    download_count = models.PositiveIntegerField(default=0)  # 已下载次数
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "文件分享"
        verbose_name_plural = "文件分享"

    @property
    def is_expired(self):
        """检查是否已过期"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False

    @property
    def is_download_limit_reached(self):
        """检查是否达到下载限制"""
        if self.download_limit:
            return self.download_count >= self.download_limit
        return False


class UploadSession(models.Model):
    """上传会话（用于断点续传）"""
    session_id = models.UUIDField(default=uuid.uuid4, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    filename = models.CharField(max_length=255)
    file_size = models.BigIntegerField()
    chunk_size = models.IntegerField(default=1024*1024)  # 分块大小
    uploaded_chunks = models.JSONField(default=list)  # 已上传的分块列表
    total_chunks = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "上传会话"
        verbose_name_plural = "上传会话"

    @property
    def upload_progress(self):
        """计算上传进度"""
        if self.total_chunks == 0:
            return 0
        return len(self.uploaded_chunks) / self.total_chunks * 100

    @property
    def is_completed(self):
        """检查是否上传完成"""
        return len(self.uploaded_chunks) == self.total_chunks