<template>
  <div class="articlelike-component">
    <div class="header">
      <h2>ArticleLike 管理</h2>
      <button @click="showCreateForm = true" class="btn btn-primary">
        添加 ArticleLike
      </button>
    </div>

    <!-- 列表 -->
    <div class="list-container">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else>
        <div v-for="item in items" :key="item.id" class="item-card">
          <div class="item-content">
            <div class="field">
              <label>Article:</label>
              <span>{{ item.article }}</span>
            </div>
            <div class="field">
              <label>User:</label>
              <span>{{ item.user }}</span>
            </div>
            <div class="field">
              <label>Is Like:</label>
              <span>{{ item.is_like }}</span>
            </div>
          </div>
          <div class="item-actions">
            <button @click="editItem(item)" class="btn btn-sm btn-secondary">编辑</button>
            <button @click="deleteItem(item.id)" class="btn btn-sm btn-danger">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑表单 -->
    <div v-if="showCreateForm || editingItem" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingItem ? '编辑' : '创建' }} ArticleLike</h3>
          <button @click="closeForm" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="submitForm" class="form">
          <div class="form-group">
            <label for="article">Article:</label>
            <select
              id="article"
              v-model="form.article"
              required
            />
          </div>
          <div class="form-group">
            <label for="user">User:</label>
            <select
              id="user"
              v-model="form.user"
              required
            />
          </div>
          <div class="form-group">
            <label for="is_like">Is Like:</label>
            <input type="checkbox"
              id="is_like"
              v-model="form.is_like"
              required
            />
          </div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              {{ editingItem ? '更新' : '创建' }}
            </button>
            <button type="button" @click="closeForm" class="btn btn-secondary">
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useArticleLikeAPI } from '../composables/useArticleLikeAPI'

export default {
  name: 'ArticleLikeComponent',
  setup() {
    const { items, loading, createArticleLike, updateArticleLike, deleteArticleLike, fetchArticleLikes } = useArticleLikeAPI()

    const showCreateForm = ref(false)
    const editingItem = ref(null)
    const form = ref({
      article: '',
      user: '',
      is_like: false,
    })

    const editItem = (item) => {
      editingItem.value = item
      form.value = { ...item }
    }

    const closeForm = () => {
      showCreateForm.value = false
      editingItem.value = null
      resetForm()
    }

    const resetForm = () => {
      form.value = {
        article: '',
        user: '',
        is_like: false,
      }
    }

    const submitForm = async () => {
      try {
        if (editingItem.value) {
          await updateArticleLike(editingItem.value.id, form.value)
        } else {
          await createArticleLike(form.value)
        }
        closeForm()
        await fetchArticleLikes()
      } catch (error) {
        console.error('提交失败:', error)
      }
    }

    const deleteItem = async (id) => {
      if (confirm('确定要删除这个ArticleLike吗？')) {
        try {
          await deleteArticleLike(id)
          await fetchArticleLikes()
        } catch (error) {
          console.error('删除失败:', error)
        }
      }
    }

    onMounted(() => {
      fetchArticleLikes()
    })

    return {
      items,
      loading,
      showCreateForm,
      editingItem,
      form,
      editItem,
      closeForm,
      submitForm,
      deleteItem
    }
  }
}
</script>

<style scoped>
.articlelike-component {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.item-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.field {
  margin-bottom: 8px;
}

.field label {
  font-weight: bold;
  margin-right: 8px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}
</style>
