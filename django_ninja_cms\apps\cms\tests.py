"""
Cms 测试用例
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status

from .models import Article, Category, Tag, Comment, ArticleView, ArticleLike, Newsletter
from .services import ArticleService, CategoryService, TagService, CommentService, ArticleViewService, ArticleLikeService, NewsletterService

User = get_user_model()

class ArticleTestCase(TestCase):
    """Article 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_article(self):
        """测试创建 Article"""
        data = {
            'name': 'Test Article',
            'description': 'Test description',
        }
        item = ArticleService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test Article')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        ArticleService.create_item(self.user, {'name': 'Item 1'})
        ArticleService.create_item(self.user, {'name': 'Item 2'})

        items = ArticleService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class ArticleAPITestCase(APITestCase):
    """Article API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_article(self):
        """测试 Article 列表 API"""
        url = reverse('cms:article-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_article(self):
        """测试创建 Article API"""
        url = reverse('cms:article-list')
        data = {
            'name': 'Test Article',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

class CategoryTestCase(TestCase):
    """Category 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_category(self):
        """测试创建 Category"""
        data = {
            'name': 'Test Category',
            'description': 'Test description',
        }
        item = CategoryService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test Category')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        CategoryService.create_item(self.user, {'name': 'Item 1'})
        CategoryService.create_item(self.user, {'name': 'Item 2'})

        items = CategoryService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class CategoryAPITestCase(APITestCase):
    """Category API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_category(self):
        """测试 Category 列表 API"""
        url = reverse('cms:category-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_category(self):
        """测试创建 Category API"""
        url = reverse('cms:category-list')
        data = {
            'name': 'Test Category',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

class TagTestCase(TestCase):
    """Tag 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_tag(self):
        """测试创建 Tag"""
        data = {
            'name': 'Test Tag',
            'description': 'Test description',
        }
        item = TagService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test Tag')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        TagService.create_item(self.user, {'name': 'Item 1'})
        TagService.create_item(self.user, {'name': 'Item 2'})

        items = TagService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class TagAPITestCase(APITestCase):
    """Tag API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_tag(self):
        """测试 Tag 列表 API"""
        url = reverse('cms:tag-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_tag(self):
        """测试创建 Tag API"""
        url = reverse('cms:tag-list')
        data = {
            'name': 'Test Tag',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

class CommentTestCase(TestCase):
    """Comment 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_comment(self):
        """测试创建 Comment"""
        data = {
            'name': 'Test Comment',
            'description': 'Test description',
        }
        item = CommentService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test Comment')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        CommentService.create_item(self.user, {'name': 'Item 1'})
        CommentService.create_item(self.user, {'name': 'Item 2'})

        items = CommentService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class CommentAPITestCase(APITestCase):
    """Comment API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_comment(self):
        """测试 Comment 列表 API"""
        url = reverse('cms:comment-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_comment(self):
        """测试创建 Comment API"""
        url = reverse('cms:comment-list')
        data = {
            'name': 'Test Comment',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

class ArticleViewTestCase(TestCase):
    """ArticleView 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_articleview(self):
        """测试创建 ArticleView"""
        data = {
            'name': 'Test ArticleView',
            'description': 'Test description',
        }
        item = ArticleViewService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test ArticleView')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        ArticleViewService.create_item(self.user, {'name': 'Item 1'})
        ArticleViewService.create_item(self.user, {'name': 'Item 2'})

        items = ArticleViewService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class ArticleViewAPITestCase(APITestCase):
    """ArticleView API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_articleview(self):
        """测试 ArticleView 列表 API"""
        url = reverse('cms:articleview-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_articleview(self):
        """测试创建 ArticleView API"""
        url = reverse('cms:articleview-list')
        data = {
            'name': 'Test ArticleView',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

class ArticleLikeTestCase(TestCase):
    """ArticleLike 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_articlelike(self):
        """测试创建 ArticleLike"""
        data = {
            'name': 'Test ArticleLike',
            'description': 'Test description',
        }
        item = ArticleLikeService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test ArticleLike')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        ArticleLikeService.create_item(self.user, {'name': 'Item 1'})
        ArticleLikeService.create_item(self.user, {'name': 'Item 2'})

        items = ArticleLikeService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class ArticleLikeAPITestCase(APITestCase):
    """ArticleLike API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_articlelike(self):
        """测试 ArticleLike 列表 API"""
        url = reverse('cms:articlelike-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_articlelike(self):
        """测试创建 ArticleLike API"""
        url = reverse('cms:articlelike-list')
        data = {
            'name': 'Test ArticleLike',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

class NewsletterTestCase(TestCase):
    """Newsletter 模型测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_newsletter(self):
        """测试创建 Newsletter"""
        data = {
            'name': 'Test Newsletter',
            'description': 'Test description',
        }
        item = NewsletterService.create_item(self.user, data)
        self.assertEqual(item.owner, self.user)
        self.assertEqual(item.name, 'Test Newsletter')

    def test_get_user_items(self):
        """测试获取用户项目列表"""
        # 创建测试数据
        NewsletterService.create_item(self.user, {'name': 'Item 1'})
        NewsletterService.create_item(self.user, {'name': 'Item 2'})

        items = NewsletterService.get_user_items(self.user)
        self.assertEqual(len(items), 2)


class NewsletterAPITestCase(APITestCase):
    """Newsletter API 测试"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_list_newsletter(self):
        """测试 Newsletter 列表 API"""
        url = reverse('cms:newsletter-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_create_newsletter(self):
        """测试创建 Newsletter API"""
        url = reverse('cms:newsletter-list')
        data = {
            'name': 'Test Newsletter',
            'description': 'Test description',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

