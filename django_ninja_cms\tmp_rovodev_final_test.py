#!/usr/bin/env python3
"""
企业级项目教程04 - CMS系统功能验证脚本
"""

import os
import sys
import django
from datetime import datetime, date

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.development')
django.setup()

def test_cms_functionality():
    """测试CMS系统的完整功能"""
    
    print("🚀 开始CMS系统功能验证...")
    print("=" * 60)
    
    try:
        # 导入模型
        from apps.cms.models import Category, Tag, Article, Comment, ArticleView, ArticleLike, Newsletter
        from apps.users.models import User
        
        print("✅ 1. 模型导入成功")
        
        # 检查用户
        user = User.objects.first()
        if not user:
            print("❌ 错误: 请先创建一个用户")
            print("   可以运行: python manage.py createsuperuser")
            return False
        
        print(f"✅ 2. 找到测试用户: {user.username}")
        
        # 测试分类创建
        print("\n📁 测试分类功能...")
        category, created = Category.objects.get_or_create(
            slug="tech",
            defaults={
                'name': "技术",
                'description': "技术相关文章分类",
                'is_active': True,
                'sort_order': 1,
                'article_count': 0,
                'seo_title': "技术文章",
                'seo_description': "最新的技术文章和教程",
                'owner': user
            }
        )
        print(f"✅ 分类{'创建' if created else '获取'}成功: {category.name}")
        
        # 测试标签创建
        print("\n🏷️ 测试标签功能...")
        tag, created = Tag.objects.get_or_create(
            slug="python",
            defaults={
                'name': "Python",
                'description': "Python编程语言相关",
                'color': "#3776ab",
                'is_active': True,
                'article_count': 0,
                'owner': user
            }
        )
        print(f"✅ 标签{'创建' if created else '获取'}成功: {tag.name}")
        
        # 测试文章创建
        print("\n📝 测试文章功能...")
        article, created = Article.objects.get_or_create(
            slug="test-article",
            defaults={
                'title': "CMS系统测试文章",
                'content': """
# CMS系统测试文章

这是一篇用于测试CMS系统功能的文章。

## 功能特性

- ✅ 文章管理
- ✅ 分类系统
- ✅ 标签系统
- ✅ 评论功能
- ✅ 点赞功能
- ✅ 浏览统计

## 总结

CMS系统运行正常！
                """.strip(),
                'summary': "这是一篇测试CMS系统各项功能的示例文章",
                'status': "published",
                'is_featured': True,
                'is_top': False,
                'view_count': 0,
                'like_count': 0,
                'comment_count': 0,
                'reading_time': 3,
                'published_at': datetime.now(),
                'author': user,
                'category': category,
                'seo_title': "CMS系统测试文章 - 功能验证",
                'seo_description': "验证CMS系统各项功能是否正常工作的测试文章",
                'seo_keywords': "CMS,测试,Django,Python",
                'owner': user
            }
        )
        
        # 添加标签关系
        article.tags.add(tag)
        print(f"✅ 文章{'创建' if created else '获取'}成功: {article.title}")
        print(f"   - 分类: {article.category.name}")
        print(f"   - 标签: {', '.join([t.name for t in article.tags.all()])}")
        
        # 测试评论功能
        print("\n💬 测试评论功能...")
        comment, created = Comment.objects.get_or_create(
            article=article,
            author=user,
            defaults={
                'content': "这是一条测试评论，用于验证评论系统功能。",
                'status': "approved",
                'is_anonymous': False,
                'author_name': user.username,
                'author_email': user.email or "<EMAIL>",
                'author_website': "",
                'ip_address': "127.0.0.1",
                'user_agent': "Test Script",
                'like_count': 0,
                'dislike_count': 0,
                'owner': user
            }
        )
        print(f"✅ 评论{'创建' if created else '获取'}成功: {comment.content[:30]}...")
        
        # 测试文章浏览记录
        print("\n👀 测试浏览记录功能...")
        article_view, created = ArticleView.objects.get_or_create(
            article=article,
            user=user,
            view_date=date.today(),
            defaults={
                'ip_address': "127.0.0.1",
                'user_agent': "Test Script",
                'referrer': "direct",
                'owner': user
            }
        )
        print(f"✅ 浏览记录{'创建' if created else '获取'}成功")
        
        # 测试文章点赞
        print("\n👍 测试点赞功能...")
        article_like, created = ArticleLike.objects.get_or_create(
            article=article,
            user=user,
            defaults={
                'is_like': True,
                'owner': user
            }
        )
        print(f"✅ 点赞记录{'创建' if created else '获取'}成功")
        
        # 测试邮件订阅
        print("\n📧 测试邮件订阅功能...")
        newsletter, created = Newsletter.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                'name': "测试用户",
                'is_active': True,
                'subscribed_at': datetime.now(),
                'owner': user
            }
        )
        print(f"✅ 邮件订阅{'创建' if created else '获取'}成功: {newsletter.email}")
        
        # 统计信息
        print("\n📊 系统统计信息:")
        print(f"   - 文章总数: {Article.objects.count()}")
        print(f"   - 分类总数: {Category.objects.count()}")
        print(f"   - 标签总数: {Tag.objects.count()}")
        print(f"   - 评论总数: {Comment.objects.count()}")
        print(f"   - 浏览记录: {ArticleView.objects.count()}")
        print(f"   - 点赞记录: {ArticleLike.objects.count()}")
        print(f"   - 邮件订阅: {Newsletter.objects.count()}")
        
        # 测试查询功能
        print("\n🔍 测试查询功能...")
        published_articles = Article.objects.filter(status="published")
        print(f"✅ 已发布文章查询: {published_articles.count()} 篇")
        
        tech_articles = Article.objects.filter(category__slug="tech")
        print(f"✅ 技术分类文章: {tech_articles.count()} 篇")
        
        python_articles = Article.objects.filter(tags__slug="python")
        print(f"✅ Python标签文章: {python_articles.count()} 篇")
        
        print("\n" + "=" * 60)
        print("🎉 CMS系统功能验证完成！")
        print("✅ 所有核心功能正常工作")
        print("✅ 数据库操作正常")
        print("✅ 模型关系正确")
        print("✅ 企业级项目教程04验证成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_imports():
    """测试API模块导入"""
    print("\n🔌 测试API模块...")
    try:
        from apps.cms.api import cms_router
        from apps.cms.schemas import ArticleSchema, CategorySchema, TagSchema
        from apps.cms.services import ArticleService, CategoryService, TagService
        print("✅ API模块导入成功")
        print(f"✅ CMS路由器: {cms_router}")
        return True
    except Exception as e:
        print(f"❌ API模块导入失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 企业级项目教程04 - CMS系统验证")
    print("=" * 60)
    
    # 测试API导入
    api_test = test_api_imports()
    
    # 测试CMS功能
    cms_test = test_cms_functionality()
    
    print("\n" + "=" * 60)
    if api_test and cms_test:
        print("🎉 所有测试通过！CMS系统完全正常！")
        print("🚀 企业级项目教程04验证成功！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败，请检查错误信息")
        sys.exit(1)