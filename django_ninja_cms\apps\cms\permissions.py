"""
Cms 权限配置
"""
from apps.permissions.models import Permission, Role

# 权限定义
PERMISSIONS = [
    # Article 权限
    {
        'codename': 'cms.article.view',
        'name': '查看 Article',
        'content_type': 'cms.article',
    },
    {
        'codename': 'cms.article.add',
        'name': '添加 Article',
        'content_type': 'cms.article',
    },
    {
        'codename': 'cms.article.change',
        'name': '修改 Article',
        'content_type': 'cms.article',
    },
    {
        'codename': 'cms.article.delete',
        'name': '删除 Article',
        'content_type': 'cms.article',
    },
    # Category 权限
    {
        'codename': 'cms.category.view',
        'name': '查看 Category',
        'content_type': 'cms.category',
    },
    {
        'codename': 'cms.category.add',
        'name': '添加 Category',
        'content_type': 'cms.category',
    },
    {
        'codename': 'cms.category.change',
        'name': '修改 Category',
        'content_type': 'cms.category',
    },
    {
        'codename': 'cms.category.delete',
        'name': '删除 Category',
        'content_type': 'cms.category',
    },
    # Tag 权限
    {
        'codename': 'cms.tag.view',
        'name': '查看 Tag',
        'content_type': 'cms.tag',
    },
    {
        'codename': 'cms.tag.add',
        'name': '添加 Tag',
        'content_type': 'cms.tag',
    },
    {
        'codename': 'cms.tag.change',
        'name': '修改 Tag',
        'content_type': 'cms.tag',
    },
    {
        'codename': 'cms.tag.delete',
        'name': '删除 Tag',
        'content_type': 'cms.tag',
    },
    # Comment 权限
    {
        'codename': 'cms.comment.view',
        'name': '查看 Comment',
        'content_type': 'cms.comment',
    },
    {
        'codename': 'cms.comment.add',
        'name': '添加 Comment',
        'content_type': 'cms.comment',
    },
    {
        'codename': 'cms.comment.change',
        'name': '修改 Comment',
        'content_type': 'cms.comment',
    },
    {
        'codename': 'cms.comment.delete',
        'name': '删除 Comment',
        'content_type': 'cms.comment',
    },
    # ArticleView 权限
    {
        'codename': 'cms.articleview.view',
        'name': '查看 ArticleView',
        'content_type': 'cms.articleview',
    },
    {
        'codename': 'cms.articleview.add',
        'name': '添加 ArticleView',
        'content_type': 'cms.articleview',
    },
    {
        'codename': 'cms.articleview.change',
        'name': '修改 ArticleView',
        'content_type': 'cms.articleview',
    },
    {
        'codename': 'cms.articleview.delete',
        'name': '删除 ArticleView',
        'content_type': 'cms.articleview',
    },
    # ArticleLike 权限
    {
        'codename': 'cms.articlelike.view',
        'name': '查看 ArticleLike',
        'content_type': 'cms.articlelike',
    },
    {
        'codename': 'cms.articlelike.add',
        'name': '添加 ArticleLike',
        'content_type': 'cms.articlelike',
    },
    {
        'codename': 'cms.articlelike.change',
        'name': '修改 ArticleLike',
        'content_type': 'cms.articlelike',
    },
    {
        'codename': 'cms.articlelike.delete',
        'name': '删除 ArticleLike',
        'content_type': 'cms.articlelike',
    },
    # Newsletter 权限
    {
        'codename': 'cms.newsletter.view',
        'name': '查看 Newsletter',
        'content_type': 'cms.newsletter',
    },
    {
        'codename': 'cms.newsletter.add',
        'name': '添加 Newsletter',
        'content_type': 'cms.newsletter',
    },
    {
        'codename': 'cms.newsletter.change',
        'name': '修改 Newsletter',
        'content_type': 'cms.newsletter',
    },
    {
        'codename': 'cms.newsletter.delete',
        'name': '删除 Newsletter',
        'content_type': 'cms.newsletter',
    },
]

# 角色定义
ROLES = [
    {
        'name': f'{app_name.title()} 管理员',
        'codename': f'{app_name}_admin',
        'permissions': [p['codename'] for p in PERMISSIONS],
    },
    {
        'name': f'{app_name.title()} 编辑者',
        'codename': f'{app_name}_editor',
        'permissions': [p['codename'] for p in PERMISSIONS if not p['codename'].endswith('.delete')],
    },
    {
        'name': f'{app_name.title()} 查看者',
        'codename': f'{app_name}_viewer',
        'permissions': [p['codename'] for p in PERMISSIONS if p['codename'].endswith('.view')],
    },
]


def setup_permissions():
    """设置权限和角色"""
    # 创建权限
    for perm_data in PERMISSIONS:
        Permission.objects.get_or_create(
            codename=perm_data['codename'],
            defaults=perm_data
        )

    # 创建角色
    for role_data in ROLES:
        role, created = Role.objects.get_or_create(
            codename=role_data['codename'],
            defaults={'name': role_data['name']}
        )

        if created:
            # 分配权限
            permissions = Permission.objects.filter(
                codename__in=role_data['permissions']
            )
            role.permissions.set(permissions)
