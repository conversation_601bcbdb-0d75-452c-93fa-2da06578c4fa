#!/usr/bin/env python
"""
测试 generate_from_config.py 代码生成工具的功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import yaml

# 添加项目路径到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

import django
django.setup()

from django.core.management import call_command
from django.core.management.base import CommandError


class TestGenerateFromConfig:
    """测试代码生成工具"""
    
    def __init__(self):
        self.test_dir = None
        self.config_file = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp(prefix="test_generate_")
        print(f"📁 测试目录: {self.test_dir}")
        
        # 创建测试配置文件
        self.config_file = os.path.join(self.test_dir, "test_config.yaml")
        test_config = {
            "app_name": "test_blog",
            "models": {
                "Post": {
                    "fields": {
                        "title": "str",
                        "content": "text",
                        "status": "choices:draft,published,archived",
                        "is_featured": "bool",
                        "view_count": "int",
                        "published_at": "datetime",
                        "author": "fk:User",
                        "category": "fk:Category",
                        "tags": "m2m:Tag"
                    },
                    "options": {
                        "ordering": ["-created_at"],
                        "verbose_name": "博客文章"
                    }
                },
                "Category": {
                    "fields": {
                        "name": "str",
                        "slug": "str",
                        "description": "text",
                        "parent": "fk:self",
                        "is_active": "bool"
                    },
                    "options": {
                        "ordering": ["name"],
                        "verbose_name": "分类"
                    }
                },
                "Tag": {
                    "fields": {
                        "name": "str",
                        "color": "str",
                        "is_active": "bool"
                    },
                    "options": {
                        "ordering": ["name"],
                        "verbose_name": "标签"
                    }
                }
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"📄 配置文件: {self.config_file}")
        
    def cleanup_test_environment(self):
        """清理测试环境"""
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
            print(f"🗑️  清理测试目录: {self.test_dir}")
    
    def test_dry_run(self):
        """测试预览模式"""
        print("\n🔍 测试预览模式...")
        try:
            call_command(
                'generate_from_config',
                config=self.config_file,
                full=True,
                with_frontend=True,
                output_dir=self.test_dir,
                dry_run=True,
                verbosity=2
            )
            print("✅ 预览模式测试通过")
            return True
        except Exception as e:
            print(f"❌ 预览模式测试失败: {e}")
            return False
    
    def test_basic_generation(self):
        """测试基本代码生成"""
        print("\n🔧 测试基本代码生成...")
        try:
            call_command(
                'generate_from_config',
                config=self.config_file,
                output_dir=self.test_dir,
                verbosity=2
            )
            
            # 检查生成的文件
            app_dir = Path(self.test_dir) / "test_blog"
            expected_files = [
                "__init__.py",
                "apps.py",
                "models.py",
                "migrations/__init__.py"
            ]
            
            for file_name in expected_files:
                file_path = app_dir / file_name
                if not file_path.exists():
                    print(f"❌ 缺少文件: {file_path}")
                    return False
                print(f"✅ 文件存在: {file_name}")
            
            print("✅ 基本代码生成测试通过")
            return True
        except Exception as e:
            print(f"❌ 基本代码生成测试失败: {e}")
            return False
    
    def test_full_generation(self):
        """测试完整功能生成"""
        print("\n🚀 测试完整功能生成...")
        try:
            call_command(
                'generate_from_config',
                config=self.config_file,
                full=True,
                output_dir=self.test_dir,
                verbosity=2
            )
            
            # 检查生成的文件
            app_dir = Path(self.test_dir) / "test_blog"
            expected_files = [
                "__init__.py",
                "apps.py",
                "models.py",
                "admin.py",
                "api.py",
                "schemas.py",
                "services.py",
                "permissions.py",
                "tests.py",
                "migrations/__init__.py"
            ]
            
            for file_name in expected_files:
                file_path = app_dir / file_name
                if not file_path.exists():
                    print(f"❌ 缺少文件: {file_path}")
                    return False
                print(f"✅ 文件存在: {file_name}")
            
            print("✅ 完整功能生成测试通过")
            return True
        except Exception as e:
            print(f"❌ 完整功能生成测试失败: {e}")
            return False
    
    def test_frontend_generation(self):
        """测试前端组件生成"""
        print("\n🎨 测试前端组件生成...")
        try:
            call_command(
                'generate_from_config',
                config=self.config_file,
                full=True,
                with_frontend=True,
                output_dir=self.test_dir,
                verbosity=2
            )
            
            # 检查前端文件
            frontend_dir = Path("frontend/src/components/test_blog")
            expected_files = [
                "Post.vue",
                "Category.vue", 
                "Tag.vue",
                "router.js"
            ]
            
            for file_name in expected_files:
                file_path = frontend_dir / file_name
                if not file_path.exists():
                    print(f"⚠️  前端文件不存在: {file_path} (这是正常的，因为前端目录可能不存在)")
                else:
                    print(f"✅ 前端文件存在: {file_name}")
            
            print("✅ 前端组件生成测试通过")
            return True
        except Exception as e:
            print(f"❌ 前端组件生成测试失败: {e}")
            return False
    
    def test_file_content_validation(self):
        """测试生成文件内容的有效性"""
        print("\n📝 测试生成文件内容...")
        try:
            app_dir = Path(self.test_dir) / "test_blog"
            
            # 检查 models.py 内容
            models_file = app_dir / "models.py"
            if models_file.exists():
                content = models_file.read_text(encoding='utf-8')
                
                # 检查是否包含预期的模型类
                expected_classes = ["class Post(BaseModel):", "class Category(BaseModel):", "class Tag(BaseModel):"]
                for class_def in expected_classes:
                    if class_def not in content:
                        print(f"❌ models.py 缺少: {class_def}")
                        return False
                    print(f"✅ models.py 包含: {class_def}")
                
                # 检查 choices 字段
                if "choices=" not in content:
                    print("❌ models.py 缺少 choices 字段")
                    return False
                print("✅ models.py 包含 choices 字段")
            
            # 检查 api.py 内容
            api_file = app_dir / "api.py"
            if api_file.exists():
                content = api_file.read_text(encoding='utf-8')
                
                # 检查是否包含 API 端点
                expected_endpoints = ["def list_post", "def create_post", "def get_post"]
                for endpoint in expected_endpoints:
                    if endpoint not in content:
                        print(f"❌ api.py 缺少: {endpoint}")
                        return False
                    print(f"✅ api.py 包含: {endpoint}")
            
            print("✅ 文件内容验证测试通过")
            return True
        except Exception as e:
            print(f"❌ 文件内容验证测试失败: {e}")
            return False
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n🚨 测试错误处理...")
        
        # 测试不存在的配置文件
        try:
            call_command(
                'generate_from_config',
                config="nonexistent_config.yaml",
                verbosity=0
            )
            print("❌ 应该抛出配置文件不存在的错误")
            return False
        except CommandError as e:
            if "配置文件不存在" in str(e):
                print("✅ 正确处理配置文件不存在错误")
            else:
                print(f"❌ 错误信息不正确: {e}")
                return False
        except Exception as e:
            print(f"❌ 意外错误: {e}")
            return False
        
        # 测试无效的配置文件
        invalid_config_file = os.path.join(self.test_dir, "invalid_config.yaml")
        with open(invalid_config_file, 'w') as f:
            f.write("invalid: yaml: content: [")
        
        try:
            call_command(
                'generate_from_config',
                config=invalid_config_file,
                verbosity=0
            )
            print("❌ 应该抛出配置文件解析错误")
            return False
        except CommandError as e:
            if "配置文件解析错误" in str(e):
                print("✅ 正确处理配置文件解析错误")
            else:
                print(f"❌ 错误信息不正确: {e}")
                return False
        except Exception as e:
            print(f"❌ 意外错误: {e}")
            return False
        
        print("✅ 错误处理测试通过")
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始测试 generate_from_config 代码生成工具")
        print("=" * 60)
        
        try:
            self.setup_test_environment()
            
            tests = [
                self.test_dry_run,
                self.test_basic_generation,
                self.test_full_generation,
                self.test_frontend_generation,
                self.test_file_content_validation,
                self.test_error_handling
            ]
            
            passed = 0
            total = len(tests)
            
            for test in tests:
                if test():
                    passed += 1
            
            print("\n" + "=" * 60)
            print(f"🎯 测试结果: {passed}/{total} 通过")
            
            if passed == total:
                print("🎉 所有测试通过！代码生成工具工作正常。")
                return True
            else:
                print("⚠️  部分测试失败，请检查代码生成工具。")
                return False
                
        finally:
            self.cleanup_test_environment()


if __name__ == "__main__":
    tester = TestGenerateFromConfig()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
