# Generated by Django 5.2.4 on 2025-07-13 11:14

import apps.storage.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("storage", "0002_alter_storagefile_file_hash"),
    ]

    operations = [
        migrations.AddField(
            model_name="storagefile",
            name="storage_backend",
            field=models.CharField(
                choices=[
                    ("local", "本地存储"),
                    ("aws_s3", "AWS S3"),
                    ("aliyun_oss", "阿里云OSS"),
                    ("tencent_cos", "腾讯云COS"),
                ],
                default="local",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="storagefile",
            name="storage_key",
            field=models.Char<PERSON>ield(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name="storagefile",
            name="storage_url",
            field=models.URLField(blank=True),
        ),
        migrations.AlterField(
            model_name="storagefile",
            name="file",
            field=models.FileField(
                blank=True, null=True, upload_to=apps.storage.models.upload_to
            ),
        ),
    ]
