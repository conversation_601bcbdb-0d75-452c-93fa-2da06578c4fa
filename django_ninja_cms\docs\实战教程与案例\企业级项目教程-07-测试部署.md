# 🧪 企业级项目教程 07 - 系统集成测试与部署

本教程将指导您完成企业级项目的测试、集成和部署，包括单元测试、集成测试、性能测试、Docker 部署和生产环境配置。

## 🎯 本教程目标

完成本教程后，您将：
- ✅ 掌握完整的测试策略和实施
- ✅ 实现自动化测试和持续集成
- ✅ 掌握 Docker 容器化部署
- ✅ 配置生产环境和监控
- ✅ 实现性能优化和安全加固
- ✅ 了解运维和维护最佳实践

## 📋 前置要求

- 完成 [前端界面开发](企业级项目教程-06-前端开发.md)
- 了解测试基础概念
- 理解 Docker 和容器化技术

## 🏗️ 测试架构设计

### 测试金字塔
```
测试策略
├── 🔬 单元测试 (Unit Tests) - 70%
│   ├── 模型测试
│   ├── 服务测试
│   ├── 工具函数测试
│   └── 组件测试
├── 🔗 集成测试 (Integration Tests) - 20%
│   ├── API 测试
│   ├── 数据库测试
│   ├── 第三方服务测试
│   └── 端到端测试
├── 🎭 端到端测试 (E2E Tests) - 10%
│   ├── 用户流程测试
│   ├── 浏览器测试
│   ├── 性能测试
│   └── 兼容性测试
└── 📊 其他测试
    ├── 安全测试
    ├── 负载测试
    ├── 可用性测试
    └── 回归测试
```

## 🧪 第一步：后端测试

### 1.1 单元测试

```python
# apps/cms/tests/test_models.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from ..models import Article, Category, Tag, Comment

User = get_user_model()

class ArticleModelTestCase(TestCase):
    """文章模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )
    
    def test_article_creation(self):
        """测试文章创建"""
        article = Article.objects.create(
            title='测试文章',
            slug='test-article',
            content='测试内容',
            author=self.user,
            category=self.category,
            owner=self.user
        )
        
        self.assertEqual(article.title, '测试文章')
        self.assertEqual(article.slug, 'test-article')
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.status, Article.STATUS_DRAFT)
        self.assertFalse(article.is_featured)
        self.assertEqual(article.view_count, 0)
    
    def test_article_str_representation(self):
        """测试文章字符串表示"""
        article = Article.objects.create(
            title='测试文章',
            slug='test-article',
            content='测试内容',
            author=self.user,
            owner=self.user
        )
        self.assertEqual(str(article), '测试文章')
    
    def test_article_absolute_url(self):
        """测试文章绝对URL"""
        article = Article.objects.create(
            title='测试文章',
            slug='test-article',
            content='测试内容',
            author=self.user,
            owner=self.user
        )
        self.assertEqual(article.get_absolute_url(), '/articles/test-article/')
    
    def test_article_reading_time(self):
        """测试阅读时间计算"""
        # 创建包含200个单词的内容
        content = ' '.join(['word'] * 200)
        article = Article.objects.create(
            title='测试文章',
            slug='test-article',
            content=content,
            author=self.user,
            owner=self.user
        )
        self.assertEqual(article.reading_time, 1)
        
        # 创建包含400个单词的内容
        content = ' '.join(['word'] * 400)
        article.content = content
        article.save()
        self.assertEqual(article.reading_time, 2)
    
    def test_article_slug_uniqueness(self):
        """测试文章slug唯一性"""
        Article.objects.create(
            title='测试文章1',
            slug='test-article',
            content='测试内容1',
            author=self.user,
            owner=self.user
        )
        
        with self.assertRaises(ValidationError):
            article2 = Article(
                title='测试文章2',
                slug='test-article',  # 重复的slug
                content='测试内容2',
                author=self.user,
                owner=self.user
            )
            article2.full_clean()

class CategoryModelTestCase(TestCase):
    """分类模型测试"""
    
    def test_category_hierarchy(self):
        """测试分类层次结构"""
        parent = Category.objects.create(
            name='父分类',
            slug='parent-category'
        )
        
        child = Category.objects.create(
            name='子分类',
            slug='child-category',
            parent=parent
        )
        
        self.assertEqual(child.parent, parent)
        self.assertIn(child, parent.children.all())
        self.assertEqual(child.full_path, '父分类 > 子分类')
    
    def test_category_descendants(self):
        """测试获取所有子分类"""
        parent = Category.objects.create(
            name='父分类',
            slug='parent-category'
        )
        
        child1 = Category.objects.create(
            name='子分类1',
            slug='child-category-1',
            parent=parent
        )
        
        child2 = Category.objects.create(
            name='子分类2',
            slug='child-category-2',
            parent=parent
        )
        
        grandchild = Category.objects.create(
            name='孙分类',
            slug='grandchild-category',
            parent=child1
        )
        
        descendants = parent.get_descendants()
        self.assertEqual(len(descendants), 3)
        self.assertIn(child1, descendants)
        self.assertIn(child2, descendants)
        self.assertIn(grandchild, descendants)

# apps/cms/tests/test_services.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from ..models import Article, Category, Tag
from ..services import ArticleService, CommentService

User = get_user_model()

class ArticleServiceTestCase(TestCase):
    """文章服务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )
    
    def test_create_article_with_tags(self):
        """测试创建带标签的文章"""
        data = {
            'title': '测试文章',
            'slug': 'test-article',
            'content': '测试内容',
            'category': self.category,
            'tags': ['标签1', '标签2', '标签3']
        }
        
        article = ArticleService.create_article(self.user, data)
        
        self.assertEqual(article.title, '测试文章')
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.tags.count(), 3)
        
        # 验证标签创建
        tag_names = list(article.tags.values_list('name', flat=True))
        self.assertIn('标签1', tag_names)
        self.assertIn('标签2', tag_names)
        self.assertIn('标签3', tag_names)
    
    def test_publish_article(self):
        """测试发布文章"""
        article = Article.objects.create(
            title='测试文章',
            slug='test-article',
            content='测试内容',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_DRAFT
        )
        
        published_article = ArticleService.publish_article(article)
        
        self.assertEqual(published_article.status, Article.STATUS_PUBLISHED)
        self.assertIsNotNone(published_article.published_at)
        
        # 验证分类文章计数更新
        self.category.refresh_from_db()
        self.assertEqual(self.category.article_count, 1)
    
    def test_get_published_articles(self):
        """测试获取已发布文章"""
        # 创建已发布文章
        published_article = Article.objects.create(
            title='已发布文章',
            slug='published-article',
            content='已发布内容',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_PUBLISHED
        )
        
        # 创建草稿文章
        Article.objects.create(
            title='草稿文章',
            slug='draft-article',
            content='草稿内容',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_DRAFT
        )
        
        published_articles = ArticleService.get_published_articles()
        
        self.assertEqual(published_articles.count(), 1)
        self.assertEqual(published_articles.first(), published_article)
    
    def test_search_articles(self):
        """测试文章搜索"""
        Article.objects.create(
            title='Python 教程',
            slug='python-tutorial',
            content='这是一个 Python 编程教程',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_PUBLISHED
        )
        
        Article.objects.create(
            title='JavaScript 指南',
            slug='javascript-guide',
            content='这是一个 JavaScript 开发指南',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_PUBLISHED
        )
        
        # 搜索 Python
        python_articles = ArticleService.get_published_articles(search='Python')
        self.assertEqual(python_articles.count(), 1)
        self.assertEqual(python_articles.first().title, 'Python 教程')
        
        # 搜索编程
        programming_articles = ArticleService.get_published_articles(search='编程')
        self.assertEqual(programming_articles.count(), 1)
```

### 1.2 API 测试

```python
# apps/cms/tests/test_api.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from ninja.testing import TestClient
from ..api import router
from ..models import Article, Category, Tag

User = get_user_model()

class ArticleAPITestCase(TestCase):
    """文章 API 测试"""
    
    def setUp(self):
        self.client = TestClient(router)
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )
        self.token = self.get_jwt_token()
    
    def get_jwt_token(self):
        """获取JWT令牌"""
        from apps.authentication.services import AuthService
        tokens = AuthService.generate_tokens(self.user)
        return tokens['access_token']
    
    def test_list_articles(self):
        """测试文章列表API"""
        # 创建测试文章
        Article.objects.create(
            title='测试文章1',
            slug='test-article-1',
            content='测试内容1',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_PUBLISHED
        )
        
        Article.objects.create(
            title='测试文章2',
            slug='test-article-2',
            content='测试内容2',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_PUBLISHED
        )
        
        response = self.client.get('/articles')
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(len(data['items']), 2)
        self.assertEqual(data['total'], 2)
    
    def test_get_article_detail(self):
        """测试文章详情API"""
        article = Article.objects.create(
            title='测试文章',
            slug='test-article',
            content='测试内容',
            author=self.user,
            category=self.category,
            owner=self.user,
            status=Article.STATUS_PUBLISHED
        )
        
        response = self.client.get(f'/articles/{article.id}')
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertEqual(data['title'], '测试文章')
        self.assertEqual(data['content'], '测试内容')
    
    def test_create_article(self):
        """测试创建文章API"""
        article_data = {
            'title': '新文章',
            'content': '新文章内容',
            'category_id': self.category.id,
            'tag_names': ['标签1', '标签2']
        }
        
        response = self.client.post(
            '/articles',
            json=article_data,
            headers={'Authorization': f'Bearer {self.token}'}
        )
        
        self.assertEqual(response.status_code, 201)
        
        # 验证文章创建
        article = Article.objects.get(title='新文章')
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.category, self.category)
        self.assertEqual(article.tags.count(), 2)
    
    def test_update_article(self):
        """测试更新文章API"""
        article = Article.objects.create(
            title='原标题',
            slug='original-title',
            content='原内容',
            author=self.user,
            category=self.category,
            owner=self.user
        )
        
        update_data = {
            'title': '新标题',
            'content': '新内容'
        }
        
        response = self.client.put(
            f'/articles/{article.id}',
            json=update_data,
            headers={'Authorization': f'Bearer {self.token}'}
        )
        
        self.assertEqual(response.status_code, 200)
        
        # 验证更新
        article.refresh_from_db()
        self.assertEqual(article.title, '新标题')
        self.assertEqual(article.content, '新内容')
    
    def test_delete_article(self):
        """测试删除文章API"""
        article = Article.objects.create(
            title='待删除文章',
            slug='to-delete-article',
            content='待删除内容',
            author=self.user,
            category=self.category,
            owner=self.user
        )
        
        response = self.client.delete(
            f'/articles/{article.id}',
            headers={'Authorization': f'Bearer {self.token}'}
        )
        
        self.assertEqual(response.status_code, 200)
        
        # 验证软删除
        article.refresh_from_db()
        self.assertEqual(article.status, Article.STATUS_DELETED)
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        response = self.client.post('/articles', json={'title': '测试'})
        self.assertEqual(response.status_code, 401)
    
    def test_permission_denied(self):
        """测试权限拒绝"""
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpass123'
        )
        
        article = Article.objects.create(
            title='他人文章',
            slug='others-article',
            content='他人内容',
            author=other_user,
            category=self.category,
            owner=other_user
        )
        
        response = self.client.delete(
            f'/articles/{article.id}',
            headers={'Authorization': f'Bearer {self.token}'}
        )
        
        self.assertEqual(response.status_code, 403)

# apps/cms/tests/test_performance.py
from django.test import TestCase, TransactionTestCase
from django.test.utils import override_settings
from django.contrib.auth import get_user_model
from django.db import connection
from django.test.utils import override_settings
import time
from ..models import Article, Category, Tag

User = get_user_model()

class PerformanceTestCase(TransactionTestCase):
    """性能测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )
    
    def test_bulk_article_creation(self):
        """测试批量创建文章性能"""
        start_time = time.time()
        
        # 批量创建1000篇文章
        articles = []
        for i in range(1000):
            articles.append(Article(
                title=f'文章 {i}',
                slug=f'article-{i}',
                content=f'内容 {i}',
                author=self.user,
                category=self.category,
                owner=self.user,
                status=Article.STATUS_PUBLISHED
            ))
        
        Article.objects.bulk_create(articles)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 批量创建应该在2秒内完成
        self.assertLess(duration, 2.0)
        self.assertEqual(Article.objects.count(), 1000)
    
    def test_query_optimization(self):
        """测试查询优化"""
        # 创建测试数据
        articles = []
        tags = []
        
        # 创建标签
        for i in range(10):
            tags.append(Tag.objects.create(
                name=f'标签{i}',
                slug=f'tag-{i}'
            ))
        
        # 创建文章
        for i in range(100):
            article = Article.objects.create(
                title=f'文章 {i}',
                slug=f'article-{i}',
                content=f'内容 {i}',
                author=self.user,
                category=self.category,
                owner=self.user,
                status=Article.STATUS_PUBLISHED
            )
            # 为每篇文章添加随机标签
            article.tags.set(tags[i % 5:i % 5 + 3])
            articles.append(article)
        
        # 测试优化前的查询
        with self.assertNumQueries(1):
            # 使用 select_related 和 prefetch_related 优化查询
            optimized_articles = Article.objects.select_related(
                'author', 'category'
            ).prefetch_related('tags').all()
            
            # 访问关联数据不应产生额外查询
            for article in optimized_articles[:10]:
                _ = article.author.username
                _ = article.category.name
                _ = list(article.tags.all())

## 🎭 第二步：前端测试

### 2.1 组件单元测试

```typescript
// src/components/__tests__/ArticleCard.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElTag, ElAvatar, ElIcon } from 'element-plus'
import ArticleCard from '@/components/business/ArticleCard.vue'
import type { Article } from '@/types/api'

// Mock router
const mockRouter = {
  push: vi.fn()
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter
}))

const mockArticle: Article = {
  id: 1,
  title: '测试文章标题',
  slug: 'test-article',
  content: '测试文章内容',
  summary: '测试文章摘要',
  status: 'published',
  is_featured: false,
  is_top: false,
  view_count: 100,
  like_count: 10,
  comment_count: 5,
  reading_time: 3,
  published_at: '2024-01-01T12:00:00Z',
  created_at: '2024-01-01T10:00:00Z',
  updated_at: '2024-01-01T12:00:00Z',
  author: {
    id: 1,
    username: 'testuser',
    first_name: '测试',
    last_name: '用户',
    avatar: 'https://example.com/avatar.jpg'
  },
  category: {
    id: 1,
    name: '测试分类',
    slug: 'test-category',
    description: '测试分类描述',
    article_count: 10,
    children: []
  },
  tags: [
    {
      id: 1,
      name: '标签1',
      slug: 'tag-1',
      color: '#007bff',
      article_count: 5
    },
    {
      id: 2,
      name: '标签2',
      slug: 'tag-2',
      color: '#28a745',
      article_count: 3
    }
  ],
  featured_image: 'https://example.com/image.jpg'
}

describe('ArticleCard', () => {
  it('renders article information correctly', () => {
    const wrapper = mount(ArticleCard, {
      props: {
        article: mockArticle
      },
      global: {
        components: {
          ElTag,
          ElAvatar,
          ElIcon
        },
        stubs: {
          'router-link': true,
          'el-icon': true
        }
      }
    })

    // 检查标题
    expect(wrapper.text()).toContain('测试文章标题')

    // 检查摘要
    expect(wrapper.text()).toContain('测试文章摘要')

    // 检查作者
    expect(wrapper.text()).toContain('testuser')

    // 检查统计信息
    expect(wrapper.text()).toContain('100') // 浏览次数
    expect(wrapper.text()).toContain('5')   // 评论数
    expect(wrapper.text()).toContain('10')  // 点赞数

    // 检查阅读时间
    expect(wrapper.text()).toContain('3 分钟阅读')
  })

  it('renders tags correctly', () => {
    const wrapper = mount(ArticleCard, {
      props: {
        article: mockArticle
      },
      global: {
        components: {
          ElTag,
          ElAvatar,
          ElIcon
        },
        stubs: {
          'router-link': true,
          'el-icon': true
        }
      }
    })

    // 检查标签数量
    const tags = wrapper.findAllComponents(ElTag)
    expect(tags).toHaveLength(2)

    // 检查标签内容
    expect(wrapper.text()).toContain('标签1')
    expect(wrapper.text()).toContain('标签2')
  })

  it('emits tag-click event when tag is clicked', async () => {
    const wrapper = mount(ArticleCard, {
      props: {
        article: mockArticle
      },
      global: {
        components: {
          ElTag,
          ElAvatar,
          ElIcon
        },
        stubs: {
          'router-link': true,
          'el-icon': true
        }
      }
    })

    // 点击第一个标签
    const firstTag = wrapper.findAllComponents(ElTag)[0]
    await firstTag.trigger('click')

    // 检查事件是否被触发
    expect(wrapper.emitted('tag-click')).toBeTruthy()
    expect(wrapper.emitted('tag-click')?.[0]).toEqual([mockArticle.tags[0]])
  })

  it('renders featured image when available', () => {
    const wrapper = mount(ArticleCard, {
      props: {
        article: mockArticle
      },
      global: {
        components: {
          ElTag,
          ElAvatar,
          ElIcon
        },
        stubs: {
          'router-link': true,
          'el-icon': true
        }
      }
    })

    const image = wrapper.find('img')
    expect(image.exists()).toBe(true)
    expect(image.attributes('src')).toBe(mockArticle.featured_image)
    expect(image.attributes('alt')).toBe(mockArticle.title)
  })

  it('does not render featured image when not available', () => {
    const articleWithoutImage = { ...mockArticle, featured_image: undefined }

    const wrapper = mount(ArticleCard, {
      props: {
        article: articleWithoutImage
      },
      global: {
        components: {
          ElTag,
          ElAvatar,
          ElIcon
        },
        stubs: {
          'router-link': true,
          'el-icon': true
        }
      }
    })

    const image = wrapper.find('img')
    expect(image.exists()).toBe(false)
  })
})

// src/stores/__tests__/cms.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useCMSStore } from '@/stores/cms'
import { CMSAPI } from '@/api/cms'

// Mock API
vi.mock('@/api/cms', () => ({
  CMSAPI: {
    getArticles: vi.fn(),
    getArticle: vi.fn(),
    createArticle: vi.fn(),
    updateArticle: vi.fn(),
    deleteArticle: vi.fn(),
    getCategories: vi.fn(),
    getTags: vi.fn()
  }
}))

describe('CMS Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('fetches articles successfully', async () => {
    const mockResponse = {
      items: [
        {
          id: 1,
          title: '测试文章',
          slug: 'test-article',
          status: 'published'
        }
      ],
      total: 1,
      page: 1,
      page_size: 20,
      total_pages: 1,
      has_next: false,
      has_prev: false
    }

    vi.mocked(CMSAPI.getArticles).mockResolvedValue(mockResponse)

    const store = useCMSStore()
    await store.fetchArticles()

    expect(CMSAPI.getArticles).toHaveBeenCalledWith({})
    expect(store.articles).toEqual(mockResponse.items)
    expect(store.articlesPagination.total).toBe(1)
    expect(store.articlesLoading).toBe(false)
  })

  it('handles fetch articles error', async () => {
    vi.mocked(CMSAPI.getArticles).mockRejectedValue(new Error('API Error'))

    const store = useCMSStore()

    try {
      await store.fetchArticles()
    } catch (error) {
      expect(error).toBeInstanceOf(Error)
    }

    expect(store.articlesLoading).toBe(false)
  })

  it('creates article successfully', async () => {
    const newArticle = {
      id: 2,
      title: '新文章',
      slug: 'new-article',
      status: 'draft'
    }

    vi.mocked(CMSAPI.createArticle).mockResolvedValue(newArticle)

    const store = useCMSStore()
    const result = await store.createArticle({
      title: '新文章',
      content: '新内容'
    })

    expect(CMSAPI.createArticle).toHaveBeenCalled()
    expect(result).toEqual(newArticle)
    expect(store.articles[0]).toEqual(newArticle)
  })

  it('filters published articles correctly', () => {
    const store = useCMSStore()
    store.articles = [
      { id: 1, status: 'published', title: '已发布文章' },
      { id: 2, status: 'draft', title: '草稿文章' },
      { id: 3, status: 'published', title: '另一篇已发布文章' }
    ]

    const publishedArticles = store.publishedArticles
    expect(publishedArticles).toHaveLength(2)
    expect(publishedArticles.every(article => article.status === 'published')).toBe(true)
  })

  it('filters featured articles correctly', () => {
    const store = useCMSStore()
    store.articles = [
      { id: 1, status: 'published', is_featured: true, title: '推荐文章' },
      { id: 2, status: 'published', is_featured: false, title: '普通文章' },
      { id: 3, status: 'draft', is_featured: true, title: '草稿推荐文章' }
    ]

    const featuredArticles = store.featuredArticles
    expect(featuredArticles).toHaveLength(1)
    expect(featuredArticles[0].title).toBe('推荐文章')
  })
})
```

### 2.2 端到端测试

```typescript
// tests/e2e/article-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('文章管理', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'admin123')
    await page.click('[data-testid="login-button"]')

    // 等待跳转到仪表板
    await expect(page).toHaveURL('/dashboard')
  })

  test('创建新文章', async ({ page }) => {
    // 导航到文章管理页面
    await page.click('[data-testid="articles-menu"]')
    await expect(page).toHaveURL('/dashboard/articles')

    // 点击创建文章按钮
    await page.click('[data-testid="create-article-button"]')
    await expect(page).toHaveURL('/dashboard/articles/create')

    // 填写文章信息
    await page.fill('[data-testid="article-title"]', '测试文章标题')
    await page.fill('[data-testid="article-content"]', '这是测试文章的内容')
    await page.fill('[data-testid="article-summary"]', '这是测试文章的摘要')

    // 选择分类
    await page.click('[data-testid="category-select"]')
    await page.click('[data-testid="category-option-1"]')

    // 添加标签
    await page.fill('[data-testid="tags-input"]', '测试标签')
    await page.press('[data-testid="tags-input"]', 'Enter')

    // 保存文章
    await page.click('[data-testid="save-article-button"]')

    // 验证成功消息
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-message"]')).toContainText('文章创建成功')

    // 验证跳转到文章列表
    await expect(page).toHaveURL('/dashboard/articles')

    // 验证文章出现在列表中
    await expect(page.locator('[data-testid="article-list"]')).toContainText('测试文章标题')
  })

  test('编辑文章', async ({ page }) => {
    // 导航到文章管理页面
    await page.goto('/dashboard/articles')

    // 点击第一篇文章的编辑按钮
    await page.click('[data-testid="edit-article-1"]')
    await expect(page).toHaveURL(/\/dashboard\/articles\/\d+\/edit/)

    // 修改标题
    await page.fill('[data-testid="article-title"]', '修改后的文章标题')

    // 保存修改
    await page.click('[data-testid="save-article-button"]')

    // 验证成功消息
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()

    // 返回列表页面
    await page.goto('/dashboard/articles')

    // 验证标题已更新
    await expect(page.locator('[data-testid="article-list"]')).toContainText('修改后的文章标题')
  })

  test('发布文章', async ({ page }) => {
    // 导航到文章管理页面
    await page.goto('/dashboard/articles')

    // 点击发布按钮
    await page.click('[data-testid="publish-article-1"]')

    // 确认发布
    await page.click('[data-testid="confirm-publish"]')

    // 验证状态更新
    await expect(page.locator('[data-testid="article-status-1"]')).toContainText('已发布')
  })

  test('删除文章', async ({ page }) => {
    // 导航到文章管理页面
    await page.goto('/dashboard/articles')

    // 记录删除前的文章数量
    const articleCountBefore = await page.locator('[data-testid="article-item"]').count()

    // 点击删除按钮
    await page.click('[data-testid="delete-article-1"]')

    // 确认删除
    await page.click('[data-testid="confirm-delete"]')

    // 验证成功消息
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()

    // 验证文章数量减少
    const articleCountAfter = await page.locator('[data-testid="article-item"]').count()
    expect(articleCountAfter).toBe(articleCountBefore - 1)
  })

  test('搜索文章', async ({ page }) => {
    // 导航到文章列表页面
    await page.goto('/articles')

    // 在搜索框中输入关键词
    await page.fill('[data-testid="search-input"]', 'Python')
    await page.press('[data-testid="search-input"]', 'Enter')

    // 验证跳转到搜索结果页面
    await expect(page).toHaveURL('/search?q=Python')

    // 验证搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-query"]')).toContainText('Python')
  })
})

// tests/e2e/user-flow.spec.ts
import { test, expect } from '@playwright/test'

test.describe('用户完整流程', () => {
  test('用户注册、登录、浏览文章流程', async ({ page }) => {
    // 1. 用户注册
    await page.goto('/register')

    await page.fill('[data-testid="username-input"]', 'newuser')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'newpass123')
    await page.fill('[data-testid="confirm-password-input"]', 'newpass123')
    await page.fill('[data-testid="first-name-input"]', '新')
    await page.fill('[data-testid="last-name-input"]', '用户')

    await page.click('[data-testid="register-button"]')

    // 验证注册成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()

    // 2. 用户登录
    await page.goto('/login')

    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'newpass123')
    await page.click('[data-testid="login-button"]')

    // 验证登录成功，跳转到首页
    await expect(page).toHaveURL('/')
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()

    // 3. 浏览文章列表
    await page.click('[data-testid="articles-nav"]')
    await expect(page).toHaveURL('/articles')

    // 验证文章列表加载
    await expect(page.locator('[data-testid="article-list"]')).toBeVisible()

    // 4. 查看文章详情
    await page.click('[data-testid="article-card"]:first-child')
    await expect(page).toHaveURL(/\/articles\/[\w-]+/)

    // 验证文章内容显示
    await expect(page.locator('[data-testid="article-title"]')).toBeVisible()
    await expect(page.locator('[data-testid="article-content"]')).toBeVisible()

    // 5. 添加评论
    await page.fill('[data-testid="comment-input"]', '这是一条测试评论')
    await page.click('[data-testid="submit-comment"]')

    // 验证评论添加成功
    await expect(page.locator('[data-testid="comment-list"]')).toContainText('这是一条测试评论')

    // 6. 用户登出
    await page.click('[data-testid="user-menu"]')
    await page.click('[data-testid="logout-button"]')

    // 验证登出成功
    await expect(page.locator('[data-testid="login-nav"]')).toBeVisible()
  })
})
```

## 🐳 第三步：Docker 部署

### 3.1 Docker 配置

```dockerfile
# Dockerfile
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_ENVIRONMENT=production

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
        curl \
        && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt /app/
COPY requirements-prod.txt /app/

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements-prod.txt

# 复制项目文件
COPY . /app/

# 创建非 root 用户
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "core.wsgi:application"]
```

```dockerfile
# Dockerfile.frontend
FROM node:18-alpine AS builder

WORKDIR /app

# 复制 package 文件
COPY frontend/package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY frontend/ .

# 构建应用
RUN npm run build

# 生产环境镜像
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: django_ninja_cms
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  web:
    build: .
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn --bind 0.0.0.0:8000 --workers 4 core.wsgi:application"
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DJANGO_ENVIRONMENT=production
      - DATABASE_URL=**************************************/django_ninja_cms
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - db
      - redis

  celery:
    build: .
    command: celery -A core worker -l info
    volumes:
      - media_volume:/app/media
    environment:
      - DJANGO_ENVIRONMENT=production
      - DATABASE_URL=**************************************/django_ninja_cms
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - db
      - redis

  celery-beat:
    build: .
    command: celery -A core beat -l info
    environment:
      - DJANGO_ENVIRONMENT=production
      - DATABASE_URL=**************************************/django_ninja_cms
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - db
      - redis

  nginx:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "80:80"
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
    depends_on:
      - web

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

### 3.2 生产环境配置

```python
# requirements-prod.txt
-r requirements.txt
gunicorn==21.2.0
psycopg2-binary==2.9.10
whitenoise==6.6.0
sentry-sdk==1.40.6
```

```python
# core/settings/production.py
from .base import *
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration

# 安全设置
DEBUG = False
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# 数据库配置
DATABASES = {
    'default': dj_database_url.parse(
        os.environ.get('DATABASE_URL'),
        conn_max_age=600,
        conn_health_checks=True,
    )
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {'max_connections': 50},
        }
    }
}

# 静态文件配置
STATIC_ROOT = os.path.join(BASE_DIR, 'static')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# 媒体文件配置
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# 安全设置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
X_FRAME_OPTIONS = 'DENY'

# HTTPS 设置
if os.environ.get('USE_HTTPS', 'False').lower() == 'true':
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Sentry 错误监控
if os.environ.get('SENTRY_DSN'):
    sentry_sdk.init(
        dsn=os.environ.get('SENTRY_DSN'),
        integrations=[
            DjangoIntegration(),
            CeleryIntegration(),
        ],
        traces_sample_rate=0.1,
        send_default_pii=True
    )

# 邮件配置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', EMAIL_HOST_USER)
```

## ✅ 完成检查

完成本教程后，您应该能够：

- [ ] 编写完整的单元测试和集成测试
- [ ] 实现前端组件和端到端测试
- [ ] 配置 Docker 容器化部署
- [ ] 设置生产环境配置
- [ ] 实现监控和日志记录
- [ ] 掌握性能优化技巧

## 📖 总结

恭喜！您已经完成了整个企业级项目开发教程系列。

**您现在拥有的技能**：
- ✅ 企业级项目架构设计
- ✅ 用户认证和权限管理
- ✅ 文件存储和处理
- ✅ 内容管理系统开发
- ✅ RESTful API 设计和实现
- ✅ 现代化前端界面开发
- ✅ 完整的测试和部署流程

**下一步建议**：
- 根据实际需求扩展功能
- 持续优化性能和用户体验
- 关注安全和稳定性
- 学习更多高级技术

---

**🎉 企业级项目开发完成！** 您现在拥有了构建和部署企业级 Web 应用的完整技能栈。
```
