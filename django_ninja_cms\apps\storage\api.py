"""
使用 Django Ninja 的文件存储 API 端点。
"""
from typing import List
from uuid import UUID
from django.http import HttpRequest, HttpResponse, Http404
from django.core.paginator import Paginator
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from ninja import Router, File, Query
from ninja.files import UploadedFile
from ninja.errors import HttpError
from ninja_jwt.authentication import J<PERSON><PERSON><PERSON>

from .models import StorageFile, FileShare, FileCategory, UploadSession
from .schemas import (
    StorageFileResponse, FileListResponse, FileShareResponse,
    CreateFileShareRequest, FileMetadataResponse, FileStatsResponse,
    FileCategorySchema, UploadSessionSchema
)
from .services import StorageService
from apps.permissions.decorators import require_permissions

User = get_user_model()
storage_router = Router()


@storage_router.post("/upload", auth=JWTAuth())
def upload_file(request: HttpRequest, file: UploadedFile = File(...)):
    """
    上传文件。
    """
    try:
        storage_file = StorageService.upload_file(
            user=request.user,
            uploaded_file=file
        )
        
        # 手动构建响应数据
        response_data = {
            "id": str(storage_file.id),
            "original_name": storage_file.original_name,
            "file_size": storage_file.file_size,
            "content_type": storage_file.content_type,
            "file_hash": storage_file.file_hash,
            "is_public": storage_file.is_public,
            "status": storage_file.status,
            "download_count": storage_file.download_count,
            "created_at": storage_file.created_at.isoformat(),
            "updated_at": storage_file.updated_at.isoformat(),
            "download_url": f"/api/storage/files/{storage_file.id}/download"
        }
        
        return response_data
        
    except ValueError as e:
        raise HttpError(400, str(e))
    except Exception as e:
        # 输出详细错误信息用于调试
        import traceback
        print(f"File upload error: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        raise HttpError(500, f"File upload failed: {str(e)}")


@storage_router.get("/files", response=FileListResponse, auth=JWTAuth())
def list_files(
    request: HttpRequest,
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    file_type: str = Query(None),
    search: str = Query(None)
):
    """
    List user's uploaded files.
    """
    queryset = StorageFile.objects.filter(uploaded_by=request.user)
    
    # Apply filters
    if file_type:
        # 根据文件扩展名过滤
        if file_type == 'image':
            queryset = queryset.filter(original_name__iregex=r'\.(jpg|jpeg|png|gif|bmp|webp)$')
        elif file_type == 'document':
            queryset = queryset.filter(original_name__iregex=r'\.(pdf|doc|docx|txt)$')
    
    if search:
        queryset = queryset.filter(original_name__icontains=search)
    
    queryset = queryset.order_by('-created_at')
    
    # Apply pagination
    paginator = Paginator(queryset, per_page)
    page_obj = paginator.get_page(page)
    
    files = []
    for storage_file in page_obj.object_list:
        file_data = {
            "id": str(storage_file.id),
            "original_name": storage_file.original_name,
            "file_size": storage_file.file_size,
            "content_type": storage_file.content_type,
            "file_hash": storage_file.file_hash,
            "is_public": storage_file.is_public,
            "status": storage_file.status,
            "download_count": storage_file.download_count,
            "created_at": storage_file.created_at.isoformat(),
            "updated_at": storage_file.updated_at.isoformat(),
            "download_url": f"/api/storage/files/{storage_file.id}/download"
        }
        files.append(file_data)
    
    return FileListResponse(
        files=files,
        total=paginator.count,
        page=page,
        per_page=per_page,
        pages=paginator.num_pages
    )


@storage_router.get("/files/{file_id}", response=FileMetadataResponse, auth=JWTAuth())
def get_file_metadata(request: HttpRequest, file_id: UUID):
    """
    Get file metadata.
    """
    storage_file = get_object_or_404(
        StorageFile,
        id=file_id,
        uploaded_by=request.user
    )
    
    # 手动构建响应数据
    return {
        "id": str(storage_file.id),
        "original_name": storage_file.original_name,
        "file_size": storage_file.file_size,
        "file_type": "image" if storage_file.is_image else "document",
        "mime_type": storage_file.content_type,
        "width": storage_file.metadata.get('width'),
        "height": storage_file.metadata.get('height'),
        "metadata": storage_file.metadata,
        "created_at": storage_file.created_at.isoformat(),
        "last_accessed": None,
        "download_count": storage_file.download_count
    }


@storage_router.get("/files/{file_id}/download", auth=JWTAuth())
def download_file(request: HttpRequest, file_id: UUID):
    """
    Download a file.
    """
    storage_file = get_object_or_404(
        StorageFile,
        id=file_id,
        uploaded_by=request.user
    )
    
    # Increment download count
    storage_file.download_count += 1
    storage_file.save(update_fields=['download_count'])
    
    # For local files, serve directly
    from django.http import FileResponse
    return FileResponse(
        storage_file.file.open(),
        as_attachment=True,
        filename=storage_file.original_name
    )


@storage_router.delete("/files/{file_id}", auth=JWTAuth())
def delete_file(request: HttpRequest, file_id: UUID):
    """
    Delete a file.
    """
    storage_file = get_object_or_404(
        StorageFile,
        id=file_id,
        uploaded_by=request.user
    )
    
    success = StorageService.delete_file(storage_file)
    
    if not success:
        raise HttpError(500, "Failed to delete file")
    
    return {"message": "File deleted successfully"}


@storage_router.post("/files/{file_id}/share", response=FileShareResponse, auth=JWTAuth())
def create_file_share(request: HttpRequest, file_id: UUID, data: CreateFileShareRequest):
    """
    Create a file share.
    """
    storage_file = get_object_or_404(
        StorageFile,
        id=file_id,
        uploaded_by=request.user
    )
    
    file_share = StorageService.create_file_share(
        storage_file=storage_file,
        shared_by=request.user,
        expires_at=data.expires_at,
        download_limit=data.download_limit
    )
    
    response_data = {
        "share_token": str(file_share.share_token),
        "expires_at": file_share.expires_at.isoformat() if file_share.expires_at else None,
        "download_limit": file_share.download_limit,
        "download_count": file_share.download_count,
        "is_active": file_share.is_active,
        "created_at": file_share.created_at.isoformat(),
        "share_url": f"/api/storage/share/{file_share.share_token}"
    }
    
    return response_data


@storage_router.get("/shares", response=List[FileShareResponse], auth=JWTAuth())
def list_file_shares(request: HttpRequest):
    """
    List user's file shares.
    """
    shares = FileShare.objects.filter(shared_by=request.user).order_by('-created_at')
    
    response_data = []
    for share in shares:
        share_data = {
            "share_token": str(share.share_token),
            "expires_at": share.expires_at.isoformat() if share.expires_at else None,
            "download_limit": share.download_limit,
            "download_count": share.download_count,
            "is_active": share.is_active,
            "created_at": share.created_at.isoformat(),
            "share_url": f"/api/storage/share/{share.share_token}"
        }
        response_data.append(share_data)
    
    return response_data


@storage_router.get("/share/{share_token}")
def access_shared_file(request: HttpRequest, share_token: str, password: str = Query(None)):
    """
    Access a shared file.
    """
    try:
        file_share = FileShare.objects.select_related('file').get(share_token=share_token)
    except FileShare.DoesNotExist:
        raise HttpError(404, "Share not found")
    
    # Check if user can access
    if not file_share.can_access(user=getattr(request, 'auth', None), password=password):
        if file_share.password_protected and not password:
            raise HttpError(401, "Password required")
        else:
            raise HttpError(403, "Access denied")
    
    # Increment download count
    file_share.increment_download_count()
    
    # Return file metadata and download URL
    file_upload = file_share.file
    download_url = file_upload.get_download_url()
    
    return {
        "file": {
            "id": str(file_upload.id),
            "original_name": file_upload.original_name,
            "file_size": file_upload.file_size,
            "file_type": file_upload.file_type,
            "mime_type": file_upload.mime_type,
            "created_at": file_upload.created_at.isoformat(),
        },
        "share": {
            "permission": file_share.permission,
            "download_count": file_share.download_count,
            "max_downloads": file_share.max_downloads,
        },
        "download_url": download_url
    }


@storage_router.delete("/shares/{share_id}", auth=JWTAuth())
def delete_file_share(request: HttpRequest, share_id: UUID):
    """
    Delete a file share.
    """
    file_share = get_object_or_404(
        FileShare,
        id=share_id,
        shared_by=request.user
    )
    
    file_share.delete()
    
    return {"message": "File share deleted successfully"}


@storage_router.get("/stats", response=FileStatsResponse, auth=JWTAuth())
@require_permissions("storage.view_stats")
def get_storage_stats(request: HttpRequest):
    """
    Get storage statistics.
    """
    from django.db.models import Sum, Count
    
    # Get user's files
    user_files = StorageFile.objects.filter(uploaded_by=request.user)
    
    # Calculate statistics
    total_files = user_files.count()
    total_size = user_files.aggregate(total=Sum('file_size'))['total'] or 0
    
    # Files by type (based on file extension)
    files_by_type = {}
    image_count = user_files.filter(original_name__iregex=r'\.(jpg|jpeg|png|gif|bmp|webp)$').count()
    if image_count > 0:
        files_by_type['image'] = image_count
    
    doc_count = user_files.filter(original_name__iregex=r'\.(pdf|doc|docx|txt)$').count()
    if doc_count > 0:
        files_by_type['document'] = doc_count
    
    # Storage usage
    storage_usage = {
        "total_size_bytes": total_size,
        "total_size_mb": round(total_size / (1024 * 1024), 2),
        "average_file_size": round(total_size / total_files, 2) if total_files > 0 else 0,
    }
    
    return FileStatsResponse(
        total_files=total_files,
        total_size=total_size,
        files_by_type=files_by_type,
        storage_usage=storage_usage
    )
