app_name: blog
models:
  Post:
    fields:
      title: str
      slug: str
      content: text
      summary: str
      is_published: bool
      view_count: int
      author: fk:User
    options:
      ordering: ['-created_at']
      verbose_name: '博客文章'

  Category:
    fields:
      name: str
      slug: str
      description: text
      is_active: bool
    options:
      ordering: ['name']
      verbose_name: '分类'

  Tag:
    fields:
      name: str
      slug: str
      color: str
      is_active: bool
    options:
      ordering: ['name']
      verbose_name: '标签'
