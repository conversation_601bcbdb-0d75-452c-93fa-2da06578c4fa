#!/usr/bin/env python
"""
简化的教程03功能验证脚本
"""
import requests
import json
import io

def test_storage_api():
    base_url = "http://localhost:8000"
    
    print("🚀 教程03文件存储系统验证")
    print("=" * 40)
    
    # 获取用户凭据
    print("请输入您的登录凭据:")
    username = input("用户名: ").strip()
    password = input("密码: ").strip()
    
    # 尝试登录
    print("\n🔐 1. 测试登录...")
    login_data = {"username": username, "password": password}
    
    try:
        response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token') or data.get('access')
            if token:
                print("✅ 登录成功")
                headers = {'Authorization': f'Bearer {token}'}
            else:
                print("❌ 未获取到token")
                print(f"响应: {data}")
                return
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应: {response.text}")
            return
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    # 测试文件上传
    print("\n📤 2. 测试文件上传...")
    test_content = b"Hello, this is a test file for tutorial 03!"
    files = {'file': ('test.txt', io.BytesIO(test_content), 'text/plain')}
    
    try:
        response = requests.post(f"{base_url}/api/storage/upload", files=files, headers=headers)
        if response.status_code == 200:
            upload_data = response.json()
            file_id = upload_data.get('id')
            print("✅ 文件上传成功")
            print(f"   文件ID: {file_id}")
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            return
    except Exception as e:
        print(f"❌ 文件上传失败: {e}")
        return
    
    # 测试文件列表
    print("\n📋 3. 测试文件列表...")
    try:
        response = requests.get(f"{base_url}/api/storage/files", headers=headers)
        if response.status_code == 200:
            files_data = response.json()
            print("✅ 获取文件列表成功")
            print(f"   总文件数: {files_data.get('total', 0)}")
        else:
            print(f"❌ 获取文件列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取文件列表失败: {e}")
    
    # 测试文件下载
    print("\n📥 4. 测试文件下载...")
    try:
        response = requests.get(f"{base_url}/api/storage/files/{file_id}/download", headers=headers)
        if response.status_code == 200:
            print("✅ 文件下载成功")
            print(f"   下载内容: {response.content[:50]}...")
        else:
            print(f"❌ 文件下载失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 文件下载失败: {e}")
    
    # 测试文件分享
    print("\n🔗 5. 测试文件分享...")
    try:
        share_data = {"expires_at": None, "download_limit": 5}
        response = requests.post(f"{base_url}/api/storage/files/{file_id}/share", 
                               json=share_data, headers=headers)
        if response.status_code == 200:
            share_info = response.json()
            print("✅ 文件分享成功")
            print(f"   分享token: {share_info.get('share_token')}")
        else:
            print(f"❌ 文件分享失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 文件分享失败: {e}")
    
    # 测试API文档
    print("\n📚 6. 测试API文档...")
    try:
        response = requests.get(f"{base_url}/api/docs/")
        if response.status_code == 200:
            print("✅ API文档访问成功")
            print(f"   访问地址: {base_url}/api/docs/")
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档访问失败: {e}")
    
    print("\n🎉 验证完成！")
    print("如果所有功能都显示 ✅，说明教程03实现成功！")

if __name__ == '__main__':
    test_storage_api()