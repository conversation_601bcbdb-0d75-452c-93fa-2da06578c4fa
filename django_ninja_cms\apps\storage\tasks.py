"""
Storage Celery tasks.
"""
import logging
from datetime import <PERSON><PERSON><PERSON>
from django.utils import timezone
from celery import shared_task
from .models import StorageFile, FileShare
from .services import StorageService

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def cleanup_expired_files(self):
    """
    Clean up expired files and shares.
    """
    try:
        now = timezone.now()
        
        # Clean up expired files
        expired_files = StorageFile.objects.filter(
            is_temporary=True,
            expires_at__lt=now
        )
        
        deleted_files_count = 0
        for file_upload in expired_files:
            if StorageService.delete_file(file_upload):
                deleted_files_count += 1
        
        # Clean up expired shares
        expired_shares = FileShare.objects.filter(expires_at__lt=now)
        deleted_shares_count = expired_shares.count()
        expired_shares.delete()
        
        logger.info(
            f"Cleaned up {deleted_files_count} expired files and "
            f"{deleted_shares_count} expired shares"
        )
        
        return f"Cleaned up {deleted_files_count} files and {deleted_shares_count} shares"
        
    except Exception as exc:
        logger.error(f"Error cleaning up expired files: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True, max_retries=3)
def cleanup_orphaned_files(self):
    """
    Clean up orphaned files (files without database records).
    """
    try:
        import os
        from django.conf import settings
        
        # This is a simplified implementation
        # In production, you'd want more sophisticated orphan detection
        
        media_root = settings.MEDIA_ROOT
        uploads_dir = os.path.join(media_root, 'uploads')
        
        if not os.path.exists(uploads_dir):
            return "No uploads directory found"
        
        orphaned_count = 0
        
        # Walk through upload directories
        for root, dirs, files in os.walk(uploads_dir):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, media_root)
                
                # Check if file exists in database
                if not StorageFile.objects.filter(file=relative_path).exists():
                    try:
                        os.remove(file_path)
                        orphaned_count += 1
                        logger.info(f"Removed orphaned file: {relative_path}")
                    except OSError:
                        logger.warning(f"Failed to remove orphaned file: {relative_path}")
        
        logger.info(f"Cleaned up {orphaned_count} orphaned files")
        return f"Cleaned up {orphaned_count} orphaned files"
        
    except Exception as exc:
        logger.error(f"Error cleaning up orphaned files: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def generate_file_thumbnails(self, file_id):
    """
    Generate thumbnails for image files.
    """
    try:
        from PIL import Image
        import os
        
        storage_file = StorageFile.objects.get(id=file_id)
        
        if not storage_file.is_image:
            return "File is not an image"
        
        # Generate thumbnail sizes
        thumbnail_sizes = [(150, 150), (300, 300), (600, 600)]
        
        for width, height in thumbnail_sizes:
            try:
                with Image.open(file_upload.file.path) as img:
                    # Calculate thumbnail size maintaining aspect ratio
                    img.thumbnail((width, height), Image.Resampling.LANCZOS)
                    
                    # Generate thumbnail filename
                    base_name, ext = os.path.splitext(file_upload.file.name)
                    thumbnail_name = f"{base_name}_thumb_{width}x{height}{ext}"
                    thumbnail_path = os.path.join(
                        os.path.dirname(file_upload.file.path),
                        f"thumb_{width}x{height}_{os.path.basename(file_upload.file.path)}"
                    )
                    
                    # Save thumbnail
                    img.save(thumbnail_path, optimize=True, quality=85)
                    
                    # Update metadata
                    if 'thumbnails' not in file_upload.metadata:
                        file_upload.metadata['thumbnails'] = {}
                    
                    file_upload.metadata['thumbnails'][f"{width}x{height}"] = {
                        'path': thumbnail_name,
                        'size': os.path.getsize(thumbnail_path)
                    }
            
            except Exception as e:
                logger.warning(f"Failed to generate {width}x{height} thumbnail: {e}")
        
        storage_file.save(update_fields=['metadata'])
        
        logger.info(f"Generated thumbnails for file: {storage_file.original_name}")
        return f"Generated thumbnails for {storage_file.original_name}"
        
    except StorageFile.DoesNotExist:
        logger.error(f"File with ID {file_id} not found")
        return "File not found"
    except Exception as exc:
        logger.error(f"Error generating thumbnails: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def calculate_storage_usage(self):
    """
    Calculate and cache storage usage statistics.
    """
    try:
        from django.db.models import Sum, Count
        from apps.core.services import CacheService
        
        # Calculate total storage usage
        total_files = StorageFile.objects.count()
        total_size = StorageFile.objects.aggregate(
            total=Sum('file_size')
        )['total'] or 0
        
        # Calculate usage by status
        usage_by_status = {}
        for status, _ in StorageFile.STATUS_CHOICES:
            count = StorageFile.objects.filter(status=status).count()
            if count > 0:
                usage_by_status[status] = count
        
        # Cache the results
        storage_stats = {
            'total_files': total_files,
            'total_size': total_size,
            'usage_by_type': usage_by_type,
            'usage_by_backend': usage_by_backend,
            'last_updated': timezone.now().isoformat()
        }
        
        CacheService.set('storage_usage_stats', storage_stats, timeout=3600)  # Cache for 1 hour
        
        logger.info(f"Calculated storage usage: {total_files} files, {total_size} bytes")
        return f"Calculated storage usage: {total_files} files, {total_size} bytes"
        
    except Exception as exc:
        logger.error(f"Error calculating storage usage: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def sync_cloud_storage(self):
    """
    Sync files with cloud storage (backup/restore operations).
    """
    try:
        # This is a placeholder for cloud storage sync operations
        # Implementation would depend on specific cloud storage requirements
        
        logger.info("Cloud storage sync completed")
        return "Cloud storage sync completed"
        
    except Exception as exc:
        logger.error(f"Error syncing cloud storage: {exc}")
        raise self.retry(exc=exc, countdown=300)  # Retry after 5 minutes
