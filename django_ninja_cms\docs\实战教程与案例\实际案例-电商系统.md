# 🛒 实际案例 - 电商系统开发

本案例将展示如何使用 Django Ninja CMS 构建一个完整的电商系统，涵盖商品管理、订单处理、支付集成、库存管理等核心功能。

## 🎯 案例目标

通过本案例，您将学会：
- ✅ 设计复杂的电商业务模型
- ✅ 实现商品管理和分类系统
- ✅ 构建购物车和订单流程
- ✅ 集成支付网关
- ✅ 实现库存管理和预警
- ✅ 构建优惠券和促销系统

## 📊 业务需求分析

### 核心功能模块
```
电商系统
├── 🛍️ 商品管理
│   ├── 商品信息管理
│   ├── 商品分类体系
│   ├── 商品规格变体
│   ├── 商品图片管理
│   └── 商品评价系统
├── 🛒 购物流程
│   ├── 购物车管理
│   ├── 订单创建
│   ├── 订单支付
│   ├── 订单跟踪
│   └── 售后服务
├── 💰 支付系统
│   ├── 多种支付方式
│   ├── 支付安全验证
│   ├── 退款处理
│   └── 财务对账
├── 📦 库存管理
│   ├── 库存实时更新
│   ├── 库存预警
│   ├── 库存盘点
│   └── 供应商管理
└── 🎁 营销系统
    ├── 优惠券系统
    ├── 促销活动
    ├── 会员等级
    └── 积分系统
```

## 🏗️ 数据模型设计

### 1. 商品相关模型

```python
# apps/ecommerce/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

User = get_user_model()

class ProductCategory(models.Model):
    """商品分类"""
    name = models.CharField(max_length=100, verbose_name='分类名称')
    slug = models.SlugField(max_length=100, unique=True, verbose_name='URL别名')
    description = models.TextField(blank=True, verbose_name='分类描述')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父分类'
    )
    image = models.ImageField(upload_to='categories/', blank=True, verbose_name='分类图片')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    # SEO 字段
    seo_title = models.CharField(max_length=200, blank=True, verbose_name='SEO标题')
    seo_description = models.TextField(blank=True, verbose_name='SEO描述')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '商品分类'
        verbose_name_plural = '商品分类'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

class Brand(models.Model):
    """品牌"""
    name = models.CharField(max_length=100, unique=True, verbose_name='品牌名称')
    slug = models.SlugField(max_length=100, unique=True, verbose_name='URL别名')
    description = models.TextField(blank=True, verbose_name='品牌描述')
    logo = models.ImageField(upload_to='brands/', blank=True, verbose_name='品牌Logo')
    website = models.URLField(blank=True, verbose_name='官方网站')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '品牌'
        verbose_name_plural = '品牌'
        ordering = ['name']

    def __str__(self):
        return self.name

class Product(models.Model):
    """商品主表"""

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '上架'),
        ('inactive', '下架'),
        ('out_of_stock', '缺货'),
    ]

    # 基本信息
    name = models.CharField(max_length=200, verbose_name='商品名称')
    slug = models.SlugField(max_length=200, unique=True, verbose_name='URL别名')
    description = models.TextField(verbose_name='商品描述')
    short_description = models.CharField(max_length=500, blank=True, verbose_name='简短描述')

    # 分类和品牌
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.SET_NULL,
        null=True,
        related_name='products',
        verbose_name='商品分类'
    )
    brand = models.ForeignKey(
        Brand,
        on_delete=models.SET_NULL,
        null=True,
        related_name='products',
        verbose_name='品牌'
    )

    # 价格信息
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name='售价'
    )
    cost_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name='成本价'
    )
    compare_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name='对比价格'
    )

    # 库存信息
    track_inventory = models.BooleanField(default=True, verbose_name='跟踪库存')
    inventory_quantity = models.IntegerField(default=0, verbose_name='库存数量')
    low_stock_threshold = models.IntegerField(default=10, verbose_name='低库存阈值')

    # 商品属性
    weight = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='重量(kg)'
    )
    dimensions = models.CharField(max_length=100, blank=True, verbose_name='尺寸(长x宽x高)')

    # 状态和设置
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    is_featured = models.BooleanField(default=False, verbose_name='是否推荐')
    is_digital = models.BooleanField(default=False, verbose_name='是否数字商品')
    requires_shipping = models.BooleanField(default=True, verbose_name='需要配送')

    # SEO 字段
    seo_title = models.CharField(max_length=200, blank=True, verbose_name='SEO标题')
    seo_description = models.TextField(blank=True, verbose_name='SEO描述')

    # 统计信息
    view_count = models.PositiveIntegerField(default=0, verbose_name='浏览次数')
    sales_count = models.PositiveIntegerField(default=0, verbose_name='销售数量')
    rating_average = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(5)],
        verbose_name='平均评分'
    )
    rating_count = models.PositiveIntegerField(default=0, verbose_name='评分数量')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '商品'
        verbose_name_plural = '商品'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['category']),
            models.Index(fields=['brand']),
            models.Index(fields=['is_featured']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_in_stock(self):
        """检查是否有库存"""
        if not self.track_inventory:
            return True
        return self.inventory_quantity > 0

    @property
    def is_low_stock(self):
        """检查是否低库存"""
        if not self.track_inventory:
            return False
        return self.inventory_quantity <= self.low_stock_threshold

    @property
    def discount_percentage(self):
        """计算折扣百分比"""
        if self.compare_price and self.compare_price > self.price:
            return int((1 - self.price / self.compare_price) * 100)
        return 0

class ProductVariant(models.Model):
    """商品变体（规格）"""
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='variants',
        verbose_name='商品'
    )

    # 变体信息
    name = models.CharField(max_length=200, verbose_name='变体名称')
    sku = models.CharField(max_length=100, unique=True, verbose_name='SKU')

    # 价格（可覆盖主商品价格）
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name='变体价格'
    )

    # 库存
    inventory_quantity = models.IntegerField(default=0, verbose_name='库存数量')

    # 变体属性
    weight = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='重量(kg)'
    )

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '商品变体'
        verbose_name_plural = '商品变体'
        unique_together = ['product', 'name']

    def __str__(self):
        return f"{self.product.name} - {self.name}"

    @property
    def effective_price(self):
        """获取有效价格"""
        return self.price if self.price else self.product.price

class ProductAttribute(models.Model):
    """商品属性"""
    name = models.CharField(max_length=100, unique=True, verbose_name='属性名称')
    display_name = models.CharField(max_length=100, verbose_name='显示名称')

    class Meta:
        verbose_name = '商品属性'
        verbose_name_plural = '商品属性'

    def __str__(self):
        return self.display_name

class ProductAttributeValue(models.Model):
    """商品属性值"""
    attribute = models.ForeignKey(
        ProductAttribute,
        on_delete=models.CASCADE,
        related_name='values',
        verbose_name='属性'
    )
    value = models.CharField(max_length=200, verbose_name='属性值')

    class Meta:
        verbose_name = '商品属性值'
        verbose_name_plural = '商品属性值'
        unique_together = ['attribute', 'value']

    def __str__(self):
        return f"{self.attribute.display_name}: {self.value}"

class ProductVariantAttribute(models.Model):
    """商品变体属性关联"""
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        related_name='attributes',
        verbose_name='变体'
    )
    attribute_value = models.ForeignKey(
        ProductAttributeValue,
        on_delete=models.CASCADE,
        verbose_name='属性值'
    )

    class Meta:
        verbose_name = '变体属性'
        verbose_name_plural = '变体属性'
        unique_together = ['variant', 'attribute_value']

class ProductImage(models.Model):
    """商品图片"""
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name='商品'
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='images',
        verbose_name='变体'
    )

    image = models.ImageField(upload_to='products/', verbose_name='图片')
    alt_text = models.CharField(max_length=200, blank=True, verbose_name='替代文本')
    is_primary = models.BooleanField(default=False, verbose_name='是否主图')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '商品图片'
        verbose_name_plural = '商品图片'
        ordering = ['sort_order', 'created_at']

    def save(self, *args, **kwargs):
        # 确保每个商品只有一张主图
        if self.is_primary:
            ProductImage.objects.filter(
                product=self.product,
                variant=self.variant,
                is_primary=True
            ).exclude(id=self.id).update(is_primary=False)
        super().save(*args, **kwargs)
```

### 2. 购物车和订单模型

```python
# 继续 apps/ecommerce/models.py

class Cart(models.Model):
    """购物车"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='cart',
        verbose_name='用户'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '购物车'
        verbose_name_plural = '购物车'

    def __str__(self):
        return f"{self.user.username}的购物车"

    @property
    def total_items(self):
        """购物车商品总数"""
        return sum(item.quantity for item in self.items.all())

    @property
    def total_price(self):
        """购物车总价"""
        return sum(item.total_price for item in self.items.all())

class CartItem(models.Model):
    """购物车商品项"""
    cart = models.ForeignKey(
        Cart,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='购物车'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        verbose_name='商品'
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='商品变体'
    )
    quantity = models.PositiveIntegerField(default=1, verbose_name='数量')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '购物车商品'
        verbose_name_plural = '购物车商品'
        unique_together = ['cart', 'product', 'variant']

    def __str__(self):
        variant_name = f" - {self.variant.name}" if self.variant else ""
        return f"{self.product.name}{variant_name} x {self.quantity}"

    @property
    def unit_price(self):
        """单价"""
        return self.variant.effective_price if self.variant else self.product.price

    @property
    def total_price(self):
        """总价"""
        return self.unit_price * self.quantity

class Order(models.Model):
    """订单"""

    STATUS_CHOICES = [
        ('pending', '待付款'),
        ('paid', '已付款'),
        ('processing', '处理中'),
        ('shipped', '已发货'),
        ('delivered', '已送达'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]

    # 订单基本信息
    order_number = models.CharField(max_length=32, unique=True, verbose_name='订单号')
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='orders',
        verbose_name='用户'
    )

    # 订单状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='订单状态')

    # 价格信息
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='小计')
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='运费')
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='税费')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='折扣金额')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='总金额')

    # 收货信息
    shipping_name = models.CharField(max_length=100, verbose_name='收货人姓名')
    shipping_phone = models.CharField(max_length=20, verbose_name='收货人电话')
    shipping_address = models.TextField(verbose_name='收货地址')
    shipping_city = models.CharField(max_length=100, verbose_name='城市')
    shipping_province = models.CharField(max_length=100, verbose_name='省份')
    shipping_postal_code = models.CharField(max_length=20, blank=True, verbose_name='邮政编码')

    # 备注信息
    notes = models.TextField(blank=True, verbose_name='订单备注')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name='付款时间')
    shipped_at = models.DateTimeField(null=True, blank=True, verbose_name='发货时间')
    delivered_at = models.DateTimeField(null=True, blank=True, verbose_name='送达时间')

    class Meta:
        verbose_name = '订单'
        verbose_name_plural = '订单'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"订单 {self.order_number}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

    def generate_order_number(self):
        """生成订单号"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"ORD{timestamp}{random_num}"

class OrderItem(models.Model):
    """订单商品项"""
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='订单'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        verbose_name='商品'
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='商品变体'
    )

    # 商品信息快照（防止商品信息变更影响历史订单）
    product_name = models.CharField(max_length=200, verbose_name='商品名称')
    variant_name = models.CharField(max_length=200, blank=True, verbose_name='变体名称')
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='单价')
    quantity = models.PositiveIntegerField(verbose_name='数量')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='总价')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '订单商品'
        verbose_name_plural = '订单商品'

    def __str__(self):
        variant_name = f" - {self.variant_name}" if self.variant_name else ""
        return f"{self.product_name}{variant_name} x {self.quantity}"

### 3. 支付和优惠券模型

```python
# 继续 apps/ecommerce/models.py

class Payment(models.Model):
    """支付记录"""

    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]

    METHOD_CHOICES = [
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('bank_card', '银行卡'),
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
    ]

    # 支付基本信息
    payment_id = models.CharField(max_length=64, unique=True, verbose_name='支付ID')
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name='订单'
    )

    # 支付信息
    method = models.CharField(max_length=20, choices=METHOD_CHOICES, verbose_name='支付方式')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='支付金额')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='支付状态')

    # 第三方支付信息
    gateway_transaction_id = models.CharField(max_length=200, blank=True, verbose_name='第三方交易ID')
    gateway_response = models.JSONField(default=dict, verbose_name='网关响应')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')

    class Meta:
        verbose_name = '支付记录'
        verbose_name_plural = '支付记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"支付 {self.payment_id}"

    def save(self, *args, **kwargs):
        if not self.payment_id:
            self.payment_id = self.generate_payment_id()
        super().save(*args, **kwargs)

    def generate_payment_id(self):
        """生成支付ID"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(100000, 999999))
        return f"PAY{timestamp}{random_num}"

class Coupon(models.Model):
    """优惠券"""

    TYPE_CHOICES = [
        ('percentage', '百分比折扣'),
        ('fixed_amount', '固定金额'),
        ('free_shipping', '免运费'),
    ]

    # 优惠券基本信息
    code = models.CharField(max_length=50, unique=True, verbose_name='优惠码')
    name = models.CharField(max_length=200, verbose_name='优惠券名称')
    description = models.TextField(blank=True, verbose_name='描述')

    # 优惠类型和金额
    discount_type = models.CharField(max_length=20, choices=TYPE_CHOICES, verbose_name='优惠类型')
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='优惠值')

    # 使用条件
    minimum_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='最低消费金额'
    )
    maximum_discount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='最大优惠金额'
    )

    # 使用限制
    usage_limit = models.PositiveIntegerField(null=True, blank=True, verbose_name='使用次数限制')
    usage_limit_per_user = models.PositiveIntegerField(null=True, blank=True, verbose_name='每用户使用次数限制')
    used_count = models.PositiveIntegerField(default=0, verbose_name='已使用次数')

    # 有效期
    start_date = models.DateTimeField(verbose_name='开始时间')
    end_date = models.DateTimeField(verbose_name='结束时间')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    # 适用范围
    applicable_categories = models.ManyToManyField(
        ProductCategory,
        blank=True,
        verbose_name='适用分类'
    )
    applicable_products = models.ManyToManyField(
        Product,
        blank=True,
        verbose_name='适用商品'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '优惠券'
        verbose_name_plural = '优惠券'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def is_valid(self, user=None, cart_total=None):
        """检查优惠券是否有效"""
        from django.utils import timezone
        now = timezone.now()

        # 检查是否启用
        if not self.is_active:
            return False, "优惠券已禁用"

        # 检查有效期
        if now < self.start_date:
            return False, "优惠券尚未生效"
        if now > self.end_date:
            return False, "优惠券已过期"

        # 检查使用次数限制
        if self.usage_limit and self.used_count >= self.usage_limit:
            return False, "优惠券使用次数已达上限"

        # 检查用户使用次数限制
        if user and self.usage_limit_per_user:
            user_usage = CouponUsage.objects.filter(
                coupon=self,
                user=user
            ).count()
            if user_usage >= self.usage_limit_per_user:
                return False, "您已达到此优惠券的使用次数上限"

        # 检查最低消费金额
        if self.minimum_amount and cart_total and cart_total < self.minimum_amount:
            return False, f"最低消费金额为 ¥{self.minimum_amount}"

        return True, "优惠券有效"

    def calculate_discount(self, amount):
        """计算优惠金额"""
        if self.discount_type == 'percentage':
            discount = amount * (self.discount_value / 100)
        elif self.discount_type == 'fixed_amount':
            discount = self.discount_value
        elif self.discount_type == 'free_shipping':
            discount = 0  # 免运费在运费计算中处理
        else:
            discount = 0

        # 应用最大优惠金额限制
        if self.maximum_discount:
            discount = min(discount, self.maximum_discount)

        return min(discount, amount)  # 优惠金额不能超过订单金额

class CouponUsage(models.Model):
    """优惠券使用记录"""
    coupon = models.ForeignKey(
        Coupon,
        on_delete=models.CASCADE,
        related_name='usages',
        verbose_name='优惠券'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='coupon_usages',
        verbose_name='用户'
    )
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='coupon_usages',
        verbose_name='订单'
    )
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='优惠金额')
    used_at = models.DateTimeField(auto_now_add=True, verbose_name='使用时间')

    class Meta:
        verbose_name = '优惠券使用记录'
        verbose_name_plural = '优惠券使用记录'
        unique_together = ['coupon', 'order']

    def __str__(self):
        return f"{self.user.username} 使用 {self.coupon.code}"

class Inventory(models.Model):
    """库存记录"""

    TYPE_CHOICES = [
        ('in', '入库'),
        ('out', '出库'),
        ('adjustment', '调整'),
        ('damaged', '损坏'),
        ('returned', '退货'),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='inventory_records',
        verbose_name='商品'
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='inventory_records',
        verbose_name='商品变体'
    )

    # 库存变动信息
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, verbose_name='变动类型')
    quantity = models.IntegerField(verbose_name='变动数量')
    reason = models.CharField(max_length=200, blank=True, verbose_name='变动原因')

    # 关联信息
    order = models.ForeignKey(
        Order,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='关联订单'
    )

    # 操作信息
    operator = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='操作员'
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '库存记录'
        verbose_name_plural = '库存记录'
        ordering = ['-created_at']

    def __str__(self):
        variant_name = f" - {self.variant.name}" if self.variant else ""
        return f"{self.product.name}{variant_name} {self.get_type_display()} {self.quantity}"

class ProductReview(models.Model):
    """商品评价"""

    RATING_CHOICES = [
        (1, '1星'),
        (2, '2星'),
        (3, '3星'),
        (4, '4星'),
        (5, '5星'),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='reviews',
        verbose_name='商品'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='product_reviews',
        verbose_name='用户'
    )
    order_item = models.ForeignKey(
        OrderItem,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='订单商品'
    )

    # 评价内容
    rating = models.IntegerField(choices=RATING_CHOICES, verbose_name='评分')
    title = models.CharField(max_length=200, blank=True, verbose_name='评价标题')
    content = models.TextField(verbose_name='评价内容')

    # 评价图片
    images = models.JSONField(default=list, verbose_name='评价图片')

    # 状态
    is_verified_purchase = models.BooleanField(default=False, verbose_name='已验证购买')
    is_approved = models.BooleanField(default=True, verbose_name='已审核')

    # 统计
    helpful_count = models.PositiveIntegerField(default=0, verbose_name='有用数')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '商品评价'
        verbose_name_plural = '商品评价'
        ordering = ['-created_at']
        unique_together = ['product', 'user', 'order_item']

    def __str__(self):
        return f"{self.user.username} 对 {self.product.name} 的评价"
```

## 🔧 业务逻辑实现

### 1. 购物车服务

```python
# apps/ecommerce/services.py
from decimal import Decimal
from django.db import transaction
from django.contrib.auth import get_user_model
from .models import (
    Product, ProductVariant, Cart, CartItem,
    Order, OrderItem, Payment, Coupon, CouponUsage, Inventory
)

User = get_user_model()

class CartService:
    """购物车服务"""

    @staticmethod
    def get_or_create_cart(user):
        """获取或创建购物车"""
        cart, created = Cart.objects.get_or_create(user=user)
        return cart

    @staticmethod
    def add_to_cart(user, product_id, variant_id=None, quantity=1):
        """添加商品到购物车"""
        cart = CartService.get_or_create_cart(user)

        try:
            product = Product.objects.get(id=product_id, status='active')
        except Product.DoesNotExist:
            raise ValueError("商品不存在或已下架")

        variant = None
        if variant_id:
            try:
                variant = ProductVariant.objects.get(
                    id=variant_id,
                    product=product,
                    is_active=True
                )
            except ProductVariant.DoesNotExist:
                raise ValueError("商品规格不存在")

        # 检查库存
        if not CartService.check_stock(product, variant, quantity):
            raise ValueError("库存不足")

        # 添加或更新购物车项
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            variant=variant,
            defaults={'quantity': quantity}
        )

        if not created:
            new_quantity = cart_item.quantity + quantity
            if not CartService.check_stock(product, variant, new_quantity):
                raise ValueError("库存不足")
            cart_item.quantity = new_quantity
            cart_item.save()

        return cart_item

    @staticmethod
    def update_cart_item(user, item_id, quantity):
        """更新购物车商品数量"""
        try:
            cart_item = CartItem.objects.get(
                id=item_id,
                cart__user=user
            )
        except CartItem.DoesNotExist:
            raise ValueError("购物车商品不存在")

        if quantity <= 0:
            cart_item.delete()
            return None

        # 检查库存
        if not CartService.check_stock(cart_item.product, cart_item.variant, quantity):
            raise ValueError("库存不足")

        cart_item.quantity = quantity
        cart_item.save()
        return cart_item

    @staticmethod
    def remove_from_cart(user, item_id):
        """从购物车移除商品"""
        try:
            cart_item = CartItem.objects.get(
                id=item_id,
                cart__user=user
            )
            cart_item.delete()
            return True
        except CartItem.DoesNotExist:
            return False

    @staticmethod
    def clear_cart(user):
        """清空购物车"""
        cart = CartService.get_or_create_cart(user)
        cart.items.all().delete()

    @staticmethod
    def check_stock(product, variant=None, quantity=1):
        """检查库存"""
        if not product.track_inventory:
            return True

        if variant:
            return variant.inventory_quantity >= quantity
        else:
            return product.inventory_quantity >= quantity

    @staticmethod
    def calculate_cart_total(cart, coupon_code=None):
        """计算购物车总价"""
        subtotal = cart.total_price
        shipping_cost = CartService.calculate_shipping_cost(cart)
        tax_amount = CartService.calculate_tax(subtotal)

        discount_amount = Decimal('0')
        coupon = None

        # 应用优惠券
        if coupon_code:
            try:
                coupon = Coupon.objects.get(code=coupon_code)
                is_valid, message = coupon.is_valid(cart.user, subtotal)
                if is_valid:
                    if coupon.discount_type == 'free_shipping':
                        shipping_cost = Decimal('0')
                    else:
                        discount_amount = coupon.calculate_discount(subtotal)
                else:
                    raise ValueError(message)
            except Coupon.DoesNotExist:
                raise ValueError("优惠券不存在")

        total = subtotal + shipping_cost + tax_amount - discount_amount

        return {
            'subtotal': subtotal,
            'shipping_cost': shipping_cost,
            'tax_amount': tax_amount,
            'discount_amount': discount_amount,
            'total': max(total, Decimal('0')),
            'coupon': coupon
        }

    @staticmethod
    def calculate_shipping_cost(cart):
        """计算运费"""
        # 简单的运费计算逻辑
        total_weight = sum(
            (item.variant.weight if item.variant and item.variant.weight
             else item.product.weight or Decimal('0')) * item.quantity
            for item in cart.items.all()
        )

        if total_weight <= 1:
            return Decimal('10.00')
        elif total_weight <= 5:
            return Decimal('20.00')
        else:
            return Decimal('30.00')

    @staticmethod
    def calculate_tax(amount):
        """计算税费"""
        # 简单的税费计算（10%）
        return amount * Decimal('0.1')
```

### 2. 订单服务

```python
# 继续 apps/ecommerce/services.py

class OrderService:
    """订单服务"""

    @staticmethod
    @transaction.atomic
    def create_order_from_cart(user, shipping_info, coupon_code=None):
        """从购物车创建订单"""
        cart = CartService.get_or_create_cart(user)

        if not cart.items.exists():
            raise ValueError("购物车为空")

        # 验证库存
        for item in cart.items.all():
            if not CartService.check_stock(item.product, item.variant, item.quantity):
                raise ValueError(f"商品 {item.product.name} 库存不足")

        # 计算价格
        price_info = CartService.calculate_cart_total(cart, coupon_code)

        # 创建订单
        order = Order.objects.create(
            user=user,
            subtotal=price_info['subtotal'],
            shipping_cost=price_info['shipping_cost'],
            tax_amount=price_info['tax_amount'],
            discount_amount=price_info['discount_amount'],
            total_amount=price_info['total'],
            **shipping_info
        )

        # 创建订单商品项
        for cart_item in cart.items.all():
            OrderItem.objects.create(
                order=order,
                product=cart_item.product,
                variant=cart_item.variant,
                product_name=cart_item.product.name,
                variant_name=cart_item.variant.name if cart_item.variant else '',
                unit_price=cart_item.unit_price,
                quantity=cart_item.quantity,
                total_price=cart_item.total_price
            )

            # 减少库存
            InventoryService.decrease_stock(
                cart_item.product,
                cart_item.variant,
                cart_item.quantity,
                order=order,
                reason='订单销售'
            )

        # 记录优惠券使用
        if price_info['coupon']:
            CouponUsage.objects.create(
                coupon=price_info['coupon'],
                user=user,
                order=order,
                discount_amount=price_info['discount_amount']
            )
            # 更新优惠券使用次数
            price_info['coupon'].used_count += 1
            price_info['coupon'].save()

        # 清空购物车
        cart.items.all().delete()

        return order

    @staticmethod
    def update_order_status(order_id, status, operator=None):
        """更新订单状态"""
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValueError("订单不存在")

        old_status = order.status
        order.status = status

        # 更新相关时间戳
        from django.utils import timezone
        now = timezone.now()

        if status == 'paid' and old_status != 'paid':
            order.paid_at = now
        elif status == 'shipped' and old_status != 'shipped':
            order.shipped_at = now
        elif status == 'delivered' and old_status != 'delivered':
            order.delivered_at = now

        order.save()

        # 记录状态变更日志
        OrderStatusLog.objects.create(
            order=order,
            old_status=old_status,
            new_status=status,
            operator=operator,
            notes=f"订单状态从 {old_status} 变更为 {status}"
        )

        return order

    @staticmethod
    @transaction.atomic
    def cancel_order(order_id, reason="", operator=None):
        """取消订单"""
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValueError("订单不存在")

        if order.status not in ['pending', 'paid']:
            raise ValueError("订单状态不允许取消")

        # 恢复库存
        for item in order.items.all():
            InventoryService.increase_stock(
                item.product,
                item.variant,
                item.quantity,
                order=order,
                reason=f"订单取消: {reason}"
            )

        # 更新订单状态
        OrderService.update_order_status(order_id, 'cancelled', operator)

        # 如果已付款，创建退款记录
        if order.status == 'paid':
            PaymentService.create_refund(order, order.total_amount, reason)

        return order

class InventoryService:
    """库存服务"""

    @staticmethod
    def increase_stock(product, variant=None, quantity=1, order=None, reason="", operator=None):
        """增加库存"""
        # 更新商品库存
        if variant:
            variant.inventory_quantity += quantity
            variant.save()
        else:
            product.inventory_quantity += quantity
            product.save()

        # 记录库存变动
        Inventory.objects.create(
            product=product,
            variant=variant,
            type='in',
            quantity=quantity,
            reason=reason,
            order=order,
            operator=operator
        )

    @staticmethod
    def decrease_stock(product, variant=None, quantity=1, order=None, reason="", operator=None):
        """减少库存"""
        # 检查库存
        if variant:
            if variant.inventory_quantity < quantity:
                raise ValueError("库存不足")
            variant.inventory_quantity -= quantity
            variant.save()
        else:
            if product.inventory_quantity < quantity:
                raise ValueError("库存不足")
            product.inventory_quantity -= quantity
            product.save()

        # 记录库存变动
        Inventory.objects.create(
            product=product,
            variant=variant,
            type='out',
            quantity=quantity,
            reason=reason,
            order=order,
            operator=operator
        )

    @staticmethod
    def get_low_stock_products():
        """获取低库存商品"""
        return Product.objects.filter(
            track_inventory=True,
            inventory_quantity__lte=models.F('low_stock_threshold')
        )

class PaymentService:
    """支付服务"""

    @staticmethod
    def create_payment(order, method, amount=None):
        """创建支付记录"""
        if amount is None:
            amount = order.total_amount

        payment = Payment.objects.create(
            order=order,
            method=method,
            amount=amount
        )

        return payment

    @staticmethod
    def process_payment(payment_id, gateway_transaction_id, gateway_response):
        """处理支付结果"""
        try:
            payment = Payment.objects.get(payment_id=payment_id)
        except Payment.DoesNotExist:
            raise ValueError("支付记录不存在")

        payment.gateway_transaction_id = gateway_transaction_id
        payment.gateway_response = gateway_response
        payment.status = 'completed'
        payment.completed_at = timezone.now()
        payment.save()

        # 更新订单状态
        OrderService.update_order_status(payment.order.id, 'paid')

        return payment

    @staticmethod
    def create_refund(order, amount, reason=""):
        """创建退款"""
        # 这里应该调用支付网关的退款API
        # 简化实现，直接创建退款记录
        refund_payment = Payment.objects.create(
            order=order,
            method=order.payments.first().method,
            amount=-amount,  # 负数表示退款
            status='completed',
            completed_at=timezone.now()
        )

        return refund_payment
```

## ✅ 案例总结

通过这个电商系统案例，我们展示了：

### 🎯 核心功能实现
- **复杂数据模型设计**: 商品变体、库存管理、订单流程
- **业务逻辑封装**: 购物车、订单、支付、库存服务
- **数据一致性保证**: 使用数据库事务确保操作原子性
- **库存管理**: 实时库存更新和预警机制

### 💡 设计亮点
- **模型关系设计**: 合理的外键和多对多关系
- **数据快照**: 订单商品信息快照，防止历史数据变更
- **状态管理**: 完整的订单和支付状态流转
- **扩展性**: 支持商品变体、优惠券、多支付方式

### 🔧 技术特色
- **服务层架构**: 业务逻辑与数据模型分离
- **事务管理**: 确保复杂操作的数据一致性
- **错误处理**: 完善的异常处理和用户提示
- **性能优化**: 合理的数据库索引和查询优化

这个案例展示了如何使用 Django Ninja CMS 构建复杂的业务系统，为实际项目开发提供了完整的参考。

---

**🎉 电商系统案例完成！** 这个案例展示了企业级电商系统的核心功能实现，可以作为实际项目的基础架构。
```