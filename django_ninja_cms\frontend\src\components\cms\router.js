/**
 * Cms 路由配置
 */
import { createRouter, createWebHistory } from 'vue-router'

import ArticleComponent from './Article.vue'
import CategoryComponent from './Category.vue'
import TagComponent from './Tag.vue'
import CommentComponent from './Comment.vue'
import ArticleViewComponent from './ArticleView.vue'
import ArticleLikeComponent from './ArticleLike.vue'
import NewsletterComponent from './Newsletter.vue'

const routes = [
  {
    path: '/article',
    name: 'Article',
    component: ArticleComponent
  },
  {
    path: '/category',
    name: 'Category',
    component: CategoryComponent
  },
  {
    path: '/tag',
    name: 'Tag',
    component: TagComponent
  },
  {
    path: '/comment',
    name: 'Comment',
    component: CommentComponent
  },
  {
    path: '/articleview',
    name: 'ArticleView',
    component: ArticleViewComponent
  },
  {
    path: '/articlelike',
    name: 'ArticleLike',
    component: ArticleLikeComponent
  },
  {
    path: '/newsletter',
    name: 'Newsletter',
    component: NewsletterComponent
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
