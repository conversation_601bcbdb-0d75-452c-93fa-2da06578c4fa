# 🔌 企业级项目教程 05 - API 接口开发

本教程将指导您设计和实现企业级的 RESTful API 接口，包括 API 设计原则、数据验证、错误处理、接口文档等核心内容。

## 🎯 本教程目标

完成本教程后，您将：
- ✅ 掌握 RESTful API 设计原则和最佳实践
- ✅ 实现完整的数据验证和序列化
- ✅ 构建统一的错误处理机制
- ✅ 实现 API 版本管理和向后兼容
- ✅ 掌握 API 性能优化技巧
- ✅ 生成完整的 API 文档

## 📋 前置要求

- 完成 [内容管理系统](企业级项目教程-04-内容管理.md)
- 了解 HTTP 协议和 REST 架构
- 理解 JSON 数据格式

## 🏗️ API 架构设计

### RESTful API 设计原则
```
RESTful API 设计
├── 📊 资源导向 (Resource-Oriented)
│   ├── 使用名词而非动词
│   ├── 资源层次结构
│   ├── 统一资源标识符
│   └── 资源状态表示
├── 🔗 HTTP 方法语义 (HTTP Methods)
│   ├── GET - 获取资源
│   ├── POST - 创建资源
│   ├── PUT - 更新资源
│   ├── PATCH - 部分更新
│   └── DELETE - 删除资源
├── 📝 状态码规范 (Status Codes)
│   ├── 2xx - 成功响应
│   ├── 4xx - 客户端错误
│   ├── 5xx - 服务器错误
│   └── 自定义业务码
└── 📋 数据格式 (Data Format)
    ├── JSON 标准格式
    ├── 统一响应结构
    ├── 分页数据格式
    └── 错误信息格式
```

## 📝 第一步：数据验证和序列化

### 1.1 Pydantic 模式设计

```python
# apps/cms/schemas.py
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

class ArticleStatus(str, Enum):
    """文章状态枚举"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    DELETED = "deleted"

class TagSchema(BaseModel):
    """标签模式"""
    id: int
    name: str
    slug: str
    color: str
    article_count: int

    class Config:
        from_attributes = True

class CategorySchema(BaseModel):
    """分类模式"""
    id: int
    name: str
    slug: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    article_count: int
    children: List['CategorySchema'] = []

    class Config:
        from_attributes = True

class AuthorSchema(BaseModel):
    """作者模式"""
    id: int
    username: str
    first_name: str
    last_name: str
    avatar: Optional[str] = None

    class Config:
        from_attributes = True

class ArticleListSchema(BaseModel):
    """文章列表模式"""
    id: int
    title: str
    slug: str
    summary: Optional[str] = None
    status: ArticleStatus
    is_featured: bool
    is_top: bool
    view_count: int
    like_count: int
    comment_count: int
    published_at: Optional[datetime] = None
    created_at: datetime

    # 关联数据
    author: AuthorSchema
    category: Optional[CategorySchema] = None
    tags: List[TagSchema] = []
    featured_image: Optional[str] = None

    # 计算字段
    reading_time: int

    class Config:
        from_attributes = True

class ArticleDetailSchema(ArticleListSchema):
    """文章详情模式"""
    content: str
    seo_title: Optional[str] = None
    seo_description: Optional[str] = None
    seo_keywords: Optional[str] = None
    updated_at: datetime

class ArticleCreateSchema(BaseModel):
    """文章创建模式"""
    title: str = Field(..., min_length=1, max_length=200, description="文章标题")
    slug: Optional[str] = Field(None, max_length=200, description="URL别名")
    content: str = Field(..., min_length=1, description="文章内容")
    summary: Optional[str] = Field(None, max_length=500, description="文章摘要")

    category_id: Optional[int] = Field(None, description="分类ID")
    tag_names: List[str] = Field(default=[], description="标签名称列表")

    status: ArticleStatus = Field(ArticleStatus.DRAFT, description="文章状态")
    is_featured: bool = Field(False, description="是否推荐")
    is_top: bool = Field(False, description="是否置顶")

    # SEO 字段
    seo_title: Optional[str] = Field(None, max_length=200, description="SEO标题")
    seo_description: Optional[str] = Field(None, max_length=300, description="SEO描述")
    seo_keywords: Optional[str] = Field(None, max_length=200, description="SEO关键词")

    featured_image_id: Optional[str] = Field(None, description="特色图片ID")

    @validator('slug')
    def validate_slug(cls, v, values):
        if not v and 'title' in values:
            # 自动生成 slug
            import re
            v = re.sub(r'[^\w\s-]', '', values['title']).strip().lower()
            v = re.sub(r'[-\s]+', '-', v)
        return v

    @validator('tag_names')
    def validate_tags(cls, v):
        # 限制标签数量
        if len(v) > 10:
            raise ValueError('标签数量不能超过10个')
        return v

class ArticleUpdateSchema(BaseModel):
    """文章更新模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    slug: Optional[str] = Field(None, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    summary: Optional[str] = Field(None, max_length=500)

    category_id: Optional[int] = None
    tag_names: Optional[List[str]] = None

    status: Optional[ArticleStatus] = None
    is_featured: Optional[bool] = None
    is_top: Optional[bool] = None

    seo_title: Optional[str] = Field(None, max_length=200)
    seo_description: Optional[str] = Field(None, max_length=300)
    seo_keywords: Optional[str] = Field(None, max_length=200)

    featured_image_id: Optional[str] = None

class CommentCreateSchema(BaseModel):
    """评论创建模式"""
    content: str = Field(..., min_length=1, max_length=1000, description="评论内容")
    parent_id: Optional[int] = Field(None, description="父评论ID")

    @validator('content')
    def validate_content(cls, v):
        # 过滤敏感词
        sensitive_words = ['spam', 'advertisement']  # 实际项目中应该从配置读取
        for word in sensitive_words:
            if word in v.lower():
                raise ValueError(f'评论包含敏感词: {word}')
        return v

class CommentSchema(BaseModel):
    """评论模式"""
    id: int
    content: str
    author: AuthorSchema
    created_at: datetime
    like_count: int
    replies: List['CommentSchema'] = []

    class Config:
        from_attributes = True

class PaginationSchema(BaseModel):
    """分页模式"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")

class PaginatedResponseSchema(BaseModel):
    """分页响应模式"""
    items: List[dict]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool

class APIResponseSchema(BaseModel):
    """统一API响应模式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[dict] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ErrorResponseSchema(BaseModel):
    """错误响应模式"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    errors: Optional[dict] = None
    timestamp: datetime = Field(default_factory=datetime.now)
```

### 1.2 高级验证器

```python
# apps/core/validators.py
from pydantic import validator
import re
from typing import Any

class BaseValidators:
    """基础验证器集合"""

    @staticmethod
    def validate_slug(slug: str) -> str:
        """验证 URL 别名"""
        if not re.match(r'^[a-z0-9-]+$', slug):
            raise ValueError('URL别名只能包含小写字母、数字和连字符')
        if slug.startswith('-') or slug.endswith('-'):
            raise ValueError('URL别名不能以连字符开头或结尾')
        if '--' in slug:
            raise ValueError('URL别名不能包含连续的连字符')
        return slug

    @staticmethod
    def validate_html_content(content: str) -> str:
        """验证和清理 HTML 内容"""
        import bleach

        # 允许的 HTML 标签
        allowed_tags = [
            'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'code', 'pre'
        ]

        # 允许的属性
        allowed_attributes = {
            'a': ['href', 'title'],
            'img': ['src', 'alt', 'title', 'width', 'height'],
        }

        # 清理 HTML
        clean_content = bleach.clean(
            content,
            tags=allowed_tags,
            attributes=allowed_attributes,
            strip=True
        )

        return clean_content

    @staticmethod
    def validate_image_url(url: str) -> str:
        """验证图片 URL"""
        if not url:
            return url

        # 检查是否为有效的图片扩展名
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
        if not any(url.lower().endswith(ext) for ext in valid_extensions):
            raise ValueError('无效的图片格式')

        return url

# 在 schemas.py 中使用验证器
class ArticleCreateSchema(BaseModel):
    # ... 其他字段

    @validator('slug')
    def validate_slug(cls, v):
        if v:
            return BaseValidators.validate_slug(v)
        return v

    @validator('content')
    def validate_content(cls, v):
        return BaseValidators.validate_html_content(v)
```

## 🔗 第二步：API 路由设计

### 2.1 RESTful 路由结构

```python
# apps/cms/api.py
from ninja import Router, Query, Path
from ninja.pagination import paginate, PageNumberPagination
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from django.db.models import Q

from apps.authentication.decorators import jwt_required
from apps.permissions.decorators import require_permission
from .models import Article, Category, Tag, Comment
from .schemas import (
    ArticleListSchema, ArticleDetailSchema, ArticleCreateSchema, ArticleUpdateSchema,
    CategorySchema, TagSchema, CommentSchema, CommentCreateSchema,
    PaginationSchema, PaginatedResponseSchema, APIResponseSchema
)
from .services import ArticleService, CommentService, CategoryService, SearchService

router = Router(tags=["内容管理"])

# ==================== 文章 API ====================

@router.get("/articles", response=List[ArticleListSchema])
@paginate(PageNumberPagination, page_size=20)
def list_articles(
    request,
    category_id: Optional[int] = Query(None, description="分类ID"),
    tag_names: Optional[str] = Query(None, description="标签名称，逗号分隔"),
    status: Optional[str] = Query(None, description="文章状态"),
    featured: Optional[bool] = Query(None, description="是否推荐"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    ordering: Optional[str] = Query("-published_at", description="排序字段")
):
    """获取文章列表"""
    queryset = Article.objects.select_related('author', 'category').prefetch_related('tags')

    # 非管理员只能看到已发布的文章
    if not request.user.is_staff:
        queryset = queryset.filter(status=Article.STATUS_PUBLISHED)

    # 分类过滤
    if category_id:
        queryset = queryset.filter(category_id=category_id)

    # 标签过滤
    if tag_names:
        tag_list = [name.strip() for name in tag_names.split(',')]
        queryset = queryset.filter(tags__name__in=tag_list).distinct()

    # 状态过滤
    if status:
        queryset = queryset.filter(status=status)

    # 推荐过滤
    if featured is not None:
        queryset = queryset.filter(is_featured=featured)

    # 搜索
    if search:
        queryset = queryset.filter(
            Q(title__icontains=search) |
            Q(content__icontains=search) |
            Q(summary__icontains=search)
        )

    # 排序
    valid_orderings = ['created_at', '-created_at', 'published_at', '-published_at', 'view_count', '-view_count']
    if ordering in valid_orderings:
        queryset = queryset.order_by(ordering)

    return queryset

@router.get("/articles/{article_id}", response=ArticleDetailSchema)
def get_article(request, article_id: int = Path(..., description="文章ID")):
    """获取文章详情"""
    article = get_object_or_404(
        Article.objects.select_related('author', 'category').prefetch_related('tags'),
        id=article_id
    )

    # 权限检查
    if article.status != Article.STATUS_PUBLISHED:
        if not request.user.is_authenticated or (
            article.author != request.user and not request.user.is_staff
        ):
            return JsonResponse({"error": "文章不存在或无权访问"}, status=404)

    # 增加浏览次数
    if article.status == Article.STATUS_PUBLISHED:
        ArticleService.increment_view_count(article)

    return article

@router.post("/articles", response=ArticleDetailSchema)
@jwt_required
@require_permission('cms.article.add')
def create_article(request, data: ArticleCreateSchema):
    """创建文章"""
    try:
        article = ArticleService.create_article(request.user, data.dict())
        return article
    except ValueError as e:
        return JsonResponse({"error": str(e)}, status=400)

@router.put("/articles/{article_id}", response=ArticleDetailSchema)
@jwt_required
def update_article(request, article_id: int, data: ArticleUpdateSchema):
    """更新文章"""
    article = get_object_or_404(Article, id=article_id)

    # 权限检查
    if article.author != request.user and not request.user.is_staff:
        return JsonResponse({"error": "无权修改此文章"}, status=403)

    # 更新字段
    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == 'tag_names':
            # 处理标签
            if value is not None:
                tags = []
                for tag_name in value:
                    tag, created = Tag.objects.get_or_create(
                        name=tag_name,
                        defaults={'slug': tag_name.lower().replace(' ', '-')}
                    )
                    tags.append(tag)
                article.tags.set(tags)
        elif hasattr(article, field):
            setattr(article, field, value)

    article.save()
    return article

@router.delete("/articles/{article_id}")
@jwt_required
def delete_article(request, article_id: int):
    """删除文章"""
    article = get_object_or_404(Article, id=article_id)

    # 权限检查
    if article.author != request.user and not request.user.is_staff:
        return JsonResponse({"error": "无权删除此文章"}, status=403)

    # 软删除
    article.status = Article.STATUS_DELETED
    article.save()

    return {"message": "文章删除成功"}

@router.post("/articles/{article_id}/publish")
@jwt_required
def publish_article(request, article_id: int):
    """发布文章"""
    article = get_object_or_404(Article, id=article_id)

    # 权限检查
    if article.author != request.user and not request.user.is_staff:
        return JsonResponse({"error": "无权发布此文章"}, status=403)

    article = ArticleService.publish_article(article)
    return {"message": "文章发布成功", "published_at": article.published_at}

# ==================== 分类 API ====================

@router.get("/categories", response=List[CategorySchema])
def list_categories(request):
    """获取分类列表"""
    return CategoryService.get_category_tree()

@router.get("/categories/{category_id}", response=CategorySchema)
def get_category(request, category_id: int):
    """获取分类详情"""
    return get_object_or_404(Category, id=category_id, is_active=True)

@router.get("/categories/{category_id}/articles", response=List[ArticleListSchema])
@paginate(PageNumberPagination, page_size=20)
def get_category_articles(request, category_id: int):
    """获取分类下的文章"""
    category = get_object_or_404(Category, id=category_id, is_active=True)
    return ArticleService.get_published_articles(category=category)

# ==================== 标签 API ====================

@router.get("/tags", response=List[TagSchema])
def list_tags(request, popular: bool = Query(False, description="是否只返回热门标签")):
    """获取标签列表"""
    queryset = Tag.objects.filter(is_active=True)

    if popular:
        queryset = queryset.filter(article_count__gt=0).order_by('-article_count')[:20]
    else:
        queryset = queryset.order_by('name')

    return list(queryset)

@router.get("/tags/{tag_id}/articles", response=List[ArticleListSchema])
@paginate(PageNumberPagination, page_size=20)
def get_tag_articles(request, tag_id: int):
    """获取标签下的文章"""
    tag = get_object_or_404(Tag, id=tag_id, is_active=True)
    return ArticleService.get_published_articles(tags=[tag])

# ==================== 评论 API ====================

@router.get("/articles/{article_id}/comments", response=List[CommentSchema])
@paginate(PageNumberPagination, page_size=20)
def list_article_comments(request, article_id: int):
    """获取文章评论"""
    article = get_object_or_404(Article, id=article_id)
    return CommentService.get_article_comments(article)

@router.post("/articles/{article_id}/comments", response=CommentSchema)
@jwt_required
def create_comment(request, article_id: int, data: CommentCreateSchema):
    """创建评论"""
    article = get_object_or_404(Article, id=article_id, status=Article.STATUS_PUBLISHED)

    # 获取客户端信息
    ip_address = request.META.get('REMOTE_ADDR', '')
    user_agent = request.META.get('HTTP_USER_AGENT', '')

    # 检查父评论
    parent = None
    if data.parent_id:
        parent = get_object_or_404(Comment, id=data.parent_id, article=article)

    comment = CommentService.create_comment(
        user=request.user,
        article=article,
        content=data.content,
        parent=parent,
        ip_address=ip_address,
        user_agent=user_agent
    )

    return comment

# ==================== 搜索 API ====================

@router.get("/search", response=List[ArticleListSchema])
@paginate(PageNumberPagination, page_size=20)
def search_articles(
    request,
    q: str = Query(..., description="搜索关键词"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    tags: Optional[str] = Query(None, description="标签，逗号分隔"),
    date_from: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    date_to: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD")
):
    """搜索文章"""
    tag_list = None
    if tags:
        tag_list = [tag.strip() for tag in tags.split(',')]

    category = None
    if category_id:
        category = get_object_or_404(Category, id=category_id)

    return SearchService.search_articles(
        query=q,
        category=category,
        tags=tag_list,
        date_from=date_from,
        date_to=date_to
    )
```

### 2.2 API 版本管理

```python
# apps/core/api_versions.py
from ninja import Router

# API 版本路由
v1_router = Router()
v2_router = Router()

# V1 API (向后兼容)
@v1_router.get("/articles")
def list_articles_v1(request):
    """V1 版本的文章列表 API"""
    # 保持旧版本的响应格式
    pass

# V2 API (新版本)
@v2_router.get("/articles")
def list_articles_v2(request):
    """V2 版本的文章列表 API"""
    # 新的响应格式和功能
    pass

# 在主路由中注册
# core/urls.py
from ninja import NinjaAPI

api = NinjaAPI(title="Django Ninja CMS API", version="2.0.0")

# 注册版本化路由
api.add_router("/v1/", v1_router)
api.add_router("/v2/", v2_router)
api.add_router("/", v2_router)  # 默认使用最新版本

## 🛡️ 第三步：错误处理和中间件

### 3.1 统一错误处理

```python
# apps/core/exceptions.py
from ninja import NinjaAPI
from ninja.responses import Response
from django.http import JsonResponse
from pydantic import ValidationError
import logging

logger = logging.getLogger(__name__)

class APIException(Exception):
    """自定义 API 异常"""
    def __init__(self, message: str, status_code: int = 400, error_code: str = None):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        super().__init__(message)

class BusinessLogicError(APIException):
    """业务逻辑错误"""
    def __init__(self, message: str, error_code: str = "BUSINESS_ERROR"):
        super().__init__(message, 400, error_code)

class PermissionDeniedError(APIException):
    """权限拒绝错误"""
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, 403, "PERMISSION_DENIED")

class ResourceNotFoundError(APIException):
    """资源不存在错误"""
    def __init__(self, message: str = "资源不存在"):
        super().__init__(message, 404, "RESOURCE_NOT_FOUND")

def setup_exception_handlers(api: NinjaAPI):
    """设置异常处理器"""

    @api.exception_handler(APIException)
    def api_exception_handler(request, exc: APIException):
        """处理自定义 API 异常"""
        return JsonResponse({
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "timestamp": datetime.now().isoformat()
        }, status=exc.status_code)

    @api.exception_handler(ValidationError)
    def validation_exception_handler(request, exc: ValidationError):
        """处理数据验证异常"""
        errors = {}
        for error in exc.errors():
            field = '.'.join(str(x) for x in error['loc'])
            errors[field] = error['msg']

        return JsonResponse({
            "success": False,
            "message": "数据验证失败",
            "error_code": "VALIDATION_ERROR",
            "errors": errors,
            "timestamp": datetime.now().isoformat()
        }, status=400)

    @api.exception_handler(PermissionError)
    def permission_exception_handler(request, exc: PermissionError):
        """处理权限异常"""
        return JsonResponse({
            "success": False,
            "message": str(exc) or "权限不足",
            "error_code": "PERMISSION_DENIED",
            "timestamp": datetime.now().isoformat()
        }, status=403)

    @api.exception_handler(Exception)
    def general_exception_handler(request, exc: Exception):
        """处理通用异常"""
        logger.error(f"Unhandled exception: {exc}", exc_info=True)

        return JsonResponse({
            "success": False,
            "message": "服务器内部错误",
            "error_code": "INTERNAL_ERROR",
            "timestamp": datetime.now().isoformat()
        }, status=500)

# 在 core/urls.py 中应用
from .exceptions import setup_exception_handlers

api = NinjaAPI(title="Django Ninja CMS API", version="2.0.0")
setup_exception_handlers(api)
```

### 3.2 API 中间件

```python
# apps/core/middleware.py
import time
import json
import logging
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse

logger = logging.getLogger('api')

class APILoggingMiddleware(MiddlewareMixin):
    """API 请求日志中间件"""

    def process_request(self, request):
        """记录请求开始时间"""
        request.start_time = time.time()

        # 记录请求信息
        if request.path.startswith('/api/'):
            logger.info(f"API Request: {request.method} {request.path}")

    def process_response(self, request, response):
        """记录响应信息"""
        if hasattr(request, 'start_time') and request.path.startswith('/api/'):
            duration = time.time() - request.start_time

            logger.info(
                f"API Response: {request.method} {request.path} "
                f"- Status: {response.status_code} "
                f"- Duration: {duration:.3f}s"
            )

        return response

class RateLimitMiddleware(MiddlewareMixin):
    """API 限流中间件"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limits = {}  # 简单的内存存储，生产环境应使用 Redis
        super().__init__(get_response)

    def process_request(self, request):
        """检查请求频率"""
        if not request.path.startswith('/api/'):
            return None

        # 获取客户端标识
        client_id = self.get_client_id(request)
        current_time = time.time()

        # 检查限流
        if client_id in self.rate_limits:
            last_request_time, request_count = self.rate_limits[client_id]

            # 重置计数器（每分钟）
            if current_time - last_request_time > 60:
                self.rate_limits[client_id] = (current_time, 1)
            else:
                # 检查是否超过限制（每分钟100次请求）
                if request_count >= 100:
                    return JsonResponse({
                        "error": "请求频率过高，请稍后再试",
                        "error_code": "RATE_LIMIT_EXCEEDED"
                    }, status=429)

                self.rate_limits[client_id] = (last_request_time, request_count + 1)
        else:
            self.rate_limits[client_id] = (current_time, 1)

        return None

    def get_client_id(self, request):
        """获取客户端标识"""
        # 优先使用用户ID，其次使用IP地址
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user_{request.user.id}"
        else:
            return f"ip_{request.META.get('REMOTE_ADDR', 'unknown')}"

class CORSMiddleware(MiddlewareMixin):
    """CORS 中间件"""

    def process_response(self, request, response):
        """添加 CORS 头"""
        if request.path.startswith('/api/'):
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
            response['Access-Control-Max-Age'] = '86400'

        return response

    def process_request(self, request):
        """处理 OPTIONS 请求"""
        if request.method == 'OPTIONS' and request.path.startswith('/api/'):
            response = JsonResponse({})
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
            return response
```

## 📊 第四步：API 性能优化

### 4.1 数据库查询优化

```python
# apps/cms/optimizations.py
from django.db.models import Prefetch, Count, Q
from .models import Article, Comment

class QueryOptimizer:
    """查询优化器"""

    @staticmethod
    def get_optimized_articles():
        """优化的文章查询"""
        return Article.objects.select_related(
            'author',
            'category',
            'featured_image'
        ).prefetch_related(
            'tags',
            Prefetch(
                'comments',
                queryset=Comment.objects.filter(
                    status=Comment.STATUS_APPROVED,
                    parent=None
                ).select_related('author')[:5]
            )
        ).annotate(
            approved_comment_count=Count(
                'comments',
                filter=Q(comments__status=Comment.STATUS_APPROVED)
            )
        )

    @staticmethod
    def get_article_with_related(article_id: int):
        """获取文章及相关数据"""
        return Article.objects.select_related(
            'author',
            'category'
        ).prefetch_related(
            'tags',
            'comments__author',
            'comments__replies__author'
        ).get(id=article_id)

# 在 API 中使用优化查询
@router.get("/articles/optimized", response=List[ArticleListSchema])
def list_optimized_articles(request):
    """优化的文章列表"""
    return QueryOptimizer.get_optimized_articles()
```

### 4.2 缓存策略

```python
# apps/core/cache.py
from django.core.cache import cache
from django.conf import settings
import json
import hashlib

class APICache:
    """API 缓存管理"""

    @staticmethod
    def get_cache_key(prefix: str, **kwargs) -> str:
        """生成缓存键"""
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}:{key_hash}"

    @staticmethod
    def cache_response(key: str, data, timeout: int = 300):
        """缓存响应数据"""
        cache.set(key, data, timeout)

    @staticmethod
    def get_cached_response(key: str):
        """获取缓存的响应"""
        return cache.get(key)

    @staticmethod
    def invalidate_pattern(pattern: str):
        """清除匹配模式的缓存"""
        # 这里需要使用支持模式匹配的缓存后端，如 Redis
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern(pattern)

# 缓存装饰器
def cache_api_response(timeout: int = 300, key_prefix: str = "api"):
    """API 响应缓存装饰器"""
    def decorator(func):
        def wrapper(request, *args, **kwargs):
            # 生成缓存键
            cache_key = APICache.get_cache_key(
                f"{key_prefix}:{func.__name__}",
                args=args,
                kwargs=kwargs,
                user_id=getattr(request.user, 'id', None)
            )

            # 尝试从缓存获取
            cached_response = APICache.get_cached_response(cache_key)
            if cached_response is not None:
                return cached_response

            # 执行原函数
            response = func(request, *args, **kwargs)

            # 缓存响应
            APICache.cache_response(cache_key, response, timeout)

            return response
        return wrapper
    return decorator

# 在 API 中使用缓存
@router.get("/articles/cached")
@cache_api_response(timeout=600, key_prefix="articles")
def list_cached_articles(request):
    """带缓存的文章列表"""
    return Article.objects.filter(status=Article.STATUS_PUBLISHED)
```

## 📚 第五步：API 文档和测试

### 5.1 自动生成文档

```python
# core/urls.py
from ninja import NinjaAPI
from ninja.openapi.docs import get_openapi_schema

api = NinjaAPI(
    title="Django Ninja CMS API",
    version="2.0.0",
    description="企业级内容管理系统 API",
    docs_url="/docs/",  # Swagger UI 地址
    openapi_url="/openapi.json",  # OpenAPI 规范地址
)

# 自定义 OpenAPI 配置
def custom_openapi():
    """自定义 OpenAPI 配置"""
    if api.openapi_schema:
        return api.openapi_schema

    openapi_schema = get_openapi_schema(
        title="Django Ninja CMS API",
        version="2.0.0",
        description="企业级内容管理系统 API 文档",
        routes=api.get_openapi_routes(),
    )

    # 添加安全配置
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        }
    }

    # 添加全局安全要求
    openapi_schema["security"] = [{"BearerAuth": []}]

    api.openapi_schema = openapi_schema
    return api.openapi_schema

api.openapi = custom_openapi
```

### 5.2 API 测试

```python
# apps/cms/tests/test_api.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from ninja.testing import TestClient
from ..api import router
from ..models import Article, Category, Tag

User = get_user_model()

class ArticleAPITestCase(TestCase):
    """文章 API 测试"""

    def setUp(self):
        self.client = TestClient(router)
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )

    def test_list_articles(self):
        """测试文章列表 API"""
        # 创建测试文章
        Article.objects.create(
            title='测试文章',
            slug='test-article',
            content='测试内容',
            author=self.user,
            category=self.category,
            status=Article.STATUS_PUBLISHED
        )

        response = self.client.get('/articles')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(len(data['items']), 1)
        self.assertEqual(data['items'][0]['title'], '测试文章')

    def test_create_article(self):
        """测试创建文章 API"""
        article_data = {
            'title': '新文章',
            'content': '新文章内容',
            'category_id': self.category.id,
            'tag_names': ['标签1', '标签2']
        }

        # 模拟认证用户
        response = self.client.post(
            '/articles',
            json=article_data,
            headers={'Authorization': f'Bearer {self.get_jwt_token()}'}
        )

        self.assertEqual(response.status_code, 201)

        # 验证文章创建
        article = Article.objects.get(title='新文章')
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.tags.count(), 2)

    def test_article_validation(self):
        """测试文章数据验证"""
        invalid_data = {
            'title': '',  # 空标题
            'content': 'x' * 10001,  # 内容过长
        }

        response = self.client.post(
            '/articles',
            json=invalid_data,
            headers={'Authorization': f'Bearer {self.get_jwt_token()}'}
        )

        self.assertEqual(response.status_code, 400)
        self.assertIn('errors', response.json())

    def get_jwt_token(self):
        """获取 JWT 令牌"""
        from apps.authentication.services import AuthService
        tokens = AuthService.generate_tokens(self.user)
        return tokens['access_token']

# 性能测试
class APIPerformanceTestCase(TestCase):
    """API 性能测试"""

    def setUp(self):
        self.client = TestClient(router)
        # 创建大量测试数据
        self.create_test_data()

    def create_test_data(self):
        """创建测试数据"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )

        category = Category.objects.create(
            name='测试分类',
            slug='test-category'
        )

        # 创建1000篇文章
        articles = []
        for i in range(1000):
            articles.append(Article(
                title=f'文章 {i}',
                slug=f'article-{i}',
                content=f'内容 {i}',
                author=user,
                category=category,
                status=Article.STATUS_PUBLISHED
            ))

        Article.objects.bulk_create(articles)

    def test_list_performance(self):
        """测试列表性能"""
        import time

        start_time = time.time()
        response = self.client.get('/articles')
        end_time = time.time()

        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 1.0)  # 响应时间应小于1秒
```

## ✅ 完成检查

完成本教程后，您应该能够：

- [ ] 设计符合 RESTful 原则的 API 接口
- [ ] 实现完整的数据验证和序列化
- [ ] 构建统一的错误处理机制
- [ ] 实现 API 版本管理
- [ ] 优化数据库查询性能
- [ ] 实现 API 缓存策略
- [ ] 生成完整的 API 文档
- [ ] 编写 API 测试用例

## 📖 下一步

恭喜！您已经构建了一个功能完整、性能优化的企业级 API 系统。

**接下来学习**：
- [前端界面开发](企业级项目教程-06-前端开发.md) - 构建 Vue.js 前端界面

**相关文档**：
- [API使用指南](API使用指南.md) - 详细的 API 使用说明
- [开发指南](开发指南.md) - API 开发最佳实践

---

**🎉 API 接口开发完成！** 您现在拥有了企业级的 RESTful API 系统，支持完整的 CRUD 操作、数据验证、错误处理和性能优化。
```